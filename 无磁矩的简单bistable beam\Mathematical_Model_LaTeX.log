This is pdfTeX, Version 3.141592653-2.6-1.40.28 (TeX Live 2025) (preloaded format=pdflatex 2025.8.19)  30 AUG 2025 01:00
entering extended mode
 \write18 enabled.
 %&-line parsing enabled.
**Mathematical_Model_LaTeX.tex
(./Mathematical_Model_LaTeX.tex
LaTeX2e <2025-06-01> patch level 1
L3 programming layer <2025-08-13>
(g:/texlive/2025/texmf-dist/tex/latex/base/article.cls
Document Class: article 2025/01/22 v1.4n Standard LaTeX document class
(g:/texlive/2025/texmf-dist/tex/latex/base/size10.clo
File: size10.clo 2025/01/22 v1.4n Standard LaTeX file (size option)
)
\c@part=\count275
\c@section=\count276
\c@subsection=\count277
\c@subsubsection=\count278
\c@paragraph=\count279
\c@subparagraph=\count280
\c@figure=\count281
\c@table=\count282
\abovecaptionskip=\skip49
\belowcaptionskip=\skip50
\bibindent=\dimen148
)
(g:/texlive/2025/texmf-dist/tex/latex/amsmath/amsmath.sty
Package: amsmath 2025/06/16 v2.17y AMS math features
\@mathmargin=\skip51

For additional information on amsmath, use the `?' option.
(g:/texlive/2025/texmf-dist/tex/latex/amsmath/amstext.sty
Package: amstext 2024/11/17 v2.01 AMS text

(g:/texlive/2025/texmf-dist/tex/latex/amsmath/amsgen.sty
File: amsgen.sty 1999/11/30 v2.0 generic functions
\@emptytoks=\toks17
\ex@=\dimen149
))
(g:/texlive/2025/texmf-dist/tex/latex/amsmath/amsbsy.sty
Package: amsbsy 1999/11/29 v1.2d Bold Symbols
\pmbraise@=\dimen150
)
(g:/texlive/2025/texmf-dist/tex/latex/amsmath/amsopn.sty
Package: amsopn 2022/04/08 v2.04 operator names
)
\inf@bad=\count283
LaTeX Info: Redefining \frac on input line 233.
\uproot@=\count284
\leftroot@=\count285
LaTeX Info: Redefining \overline on input line 398.
LaTeX Info: Redefining \colon on input line 409.
\classnum@=\count286
\DOTSCASE@=\count287
LaTeX Info: Redefining \ldots on input line 495.
LaTeX Info: Redefining \dots on input line 498.
LaTeX Info: Redefining \cdots on input line 619.
\Mathstrutbox@=\box53
\strutbox@=\box54
LaTeX Info: Redefining \big on input line 721.
LaTeX Info: Redefining \Big on input line 722.
LaTeX Info: Redefining \bigg on input line 723.
LaTeX Info: Redefining \Bigg on input line 724.
\big@size=\dimen151
LaTeX Font Info:    Redeclaring font encoding OML on input line 742.
LaTeX Font Info:    Redeclaring font encoding OMS on input line 743.
\macc@depth=\count288
LaTeX Info: Redefining \bmod on input line 904.
LaTeX Info: Redefining \pmod on input line 909.
LaTeX Info: Redefining \smash on input line 939.
LaTeX Info: Redefining \relbar on input line 969.
LaTeX Info: Redefining \Relbar on input line 970.
\c@MaxMatrixCols=\count289
\dotsspace@=\muskip17
\c@parentequation=\count290
\dspbrk@lvl=\count291
\tag@help=\toks18
\row@=\count292
\column@=\count293
\maxfields@=\count294
\andhelp@=\toks19
\eqnshift@=\dimen152
\alignsep@=\dimen153
\tagshift@=\dimen154
\tagwidth@=\dimen155
\totwidth@=\dimen156
\lineht@=\dimen157
\@envbody=\toks20
\multlinegap=\skip52
\multlinetaggap=\skip53
\mathdisplay@stack=\toks21
LaTeX Info: Redefining \[ on input line 2949.
LaTeX Info: Redefining \] on input line 2950.
)
(g:/texlive/2025/texmf-dist/tex/latex/amsfonts/amsfonts.sty
Package: amsfonts 2013/01/14 v3.01 Basic AMSFonts support
\symAMSa=\mathgroup4
\symAMSb=\mathgroup5
LaTeX Font Info:    Redeclaring math symbol \hbar on input line 98.
LaTeX Font Info:    Overwriting math alphabet `\mathfrak' in version `bold'
(Font)                  U/euf/m/n --> U/euf/b/n on input line 106.
)
(g:/texlive/2025/texmf-dist/tex/latex/amsfonts/amssymb.sty
Package: amssymb 2013/01/14 v3.01 AMS font symbols
)
(g:/texlive/2025/texmf-dist/tex/latex/hyperref/hyperref.sty
Package: hyperref 2025-07-12 v7.01o Hypertext links for LaTeX

(g:/texlive/2025/texmf-dist/tex/generic/iftex/iftex.sty
Package: iftex 2024/12/12 v1.0g TeX engine tests
)
(g:/texlive/2025/texmf-dist/tex/latex/graphics/keyval.sty
Package: keyval 2022/05/29 v1.15 key=value parser (DPC)
\KV@toks@=\toks22
)
(g:/texlive/2025/texmf-dist/tex/latex/kvsetkeys/kvsetkeys.sty
Package: kvsetkeys 2022-10-05 v1.19 Key value parser (HO)
)
(g:/texlive/2025/texmf-dist/tex/generic/kvdefinekeys/kvdefinekeys.sty
Package: kvdefinekeys 2019-12-19 v1.6 Define keys (HO)
)
(g:/texlive/2025/texmf-dist/tex/generic/pdfescape/pdfescape.sty
Package: pdfescape 2019/12/09 v1.15 Implements pdfTeX's escape features (HO)

(g:/texlive/2025/texmf-dist/tex/generic/ltxcmds/ltxcmds.sty
Package: ltxcmds 2023-12-04 v1.26 LaTeX kernel commands for general use (HO)
)
(g:/texlive/2025/texmf-dist/tex/generic/pdftexcmds/pdftexcmds.sty
Package: pdftexcmds 2020-06-27 v0.33 Utility functions of pdfTeX for LuaTeX (HO
)

(g:/texlive/2025/texmf-dist/tex/generic/infwarerr/infwarerr.sty
Package: infwarerr 2019/12/03 v1.5 Providing info/warning/error messages (HO)
)
Package pdftexcmds Info: \pdf@primitive is available.
Package pdftexcmds Info: \pdf@ifprimitive is available.
Package pdftexcmds Info: \pdfdraftmode found.
))
(g:/texlive/2025/texmf-dist/tex/latex/hycolor/hycolor.sty
Package: hycolor 2020-01-27 v1.10 Color options for hyperref/bookmark (HO)
)
(g:/texlive/2025/texmf-dist/tex/latex/hyperref/nameref.sty
Package: nameref 2025-06-21 v2.57 Cross-referencing by name of section

(g:/texlive/2025/texmf-dist/tex/latex/refcount/refcount.sty
Package: refcount 2019/12/15 v3.6 Data extraction from label references (HO)
)
(g:/texlive/2025/texmf-dist/tex/generic/gettitlestring/gettitlestring.sty
Package: gettitlestring 2019/12/15 v1.6 Cleanup title references (HO)

(g:/texlive/2025/texmf-dist/tex/latex/kvoptions/kvoptions.sty
Package: kvoptions 2022-06-15 v3.15 Key value format for package options (HO)
))
\c@section@level=\count295
)
(g:/texlive/2025/texmf-dist/tex/latex/etoolbox/etoolbox.sty
Package: etoolbox 2025/02/11 v2.5l e-TeX tools for LaTeX (JAW)
\etb@tempcnta=\count296
)
(g:/texlive/2025/texmf-dist/tex/generic/stringenc/stringenc.sty
Package: stringenc 2019/11/29 v1.12 Convert strings between diff. encodings (HO
)
)
\@linkdim=\dimen158
\Hy@linkcounter=\count297
\Hy@pagecounter=\count298

(g:/texlive/2025/texmf-dist/tex/latex/hyperref/pd1enc.def
File: pd1enc.def 2025-07-12 v7.01o Hyperref: PDFDocEncoding definition (HO)
Now handling font encoding PD1 ...
... no UTF-8 mapping file for font encoding PD1
)
(g:/texlive/2025/texmf-dist/tex/generic/intcalc/intcalc.sty
Package: intcalc 2019/12/15 v1.3 Expandable calculations with integers (HO)
)
\Hy@SavedSpaceFactor=\count299

(g:/texlive/2025/texmf-dist/tex/latex/hyperref/puenc.def
File: puenc.def 2025-07-12 v7.01o Hyperref: PDF Unicode definition (HO)
Now handling font encoding PU ...
... no UTF-8 mapping file for font encoding PU
)
Package hyperref Info: Hyper figures OFF on input line 4195.
Package hyperref Info: Link nesting OFF on input line 4200.
Package hyperref Info: Hyper index ON on input line 4203.
Package hyperref Info: Plain pages OFF on input line 4210.
Package hyperref Info: Backreferencing OFF on input line 4215.
Package hyperref Info: Implicit mode ON; LaTeX internals redefined.
Package hyperref Info: Bookmarks ON on input line 4462.
\c@Hy@tempcnt=\count300

(g:/texlive/2025/texmf-dist/tex/latex/url/url.sty
\Urlmuskip=\muskip18
Package: url 2013/09/16  ver 3.4  Verb mode for urls, etc.
)
LaTeX Info: Redefining \url on input line 4801.
\XeTeXLinkMargin=\dimen159

(g:/texlive/2025/texmf-dist/tex/generic/bitset/bitset.sty
Package: bitset 2019/12/09 v1.3 Handle bit-vector datatype (HO)

(g:/texlive/2025/texmf-dist/tex/generic/bigintcalc/bigintcalc.sty
Package: bigintcalc 2019/12/15 v1.5 Expandable calculations on big integers (HO
)
))
\Fld@menulength=\count301
\Field@Width=\dimen160
\Fld@charsize=\dimen161
Package hyperref Info: Hyper figures OFF on input line 6078.
Package hyperref Info: Link nesting OFF on input line 6083.
Package hyperref Info: Hyper index ON on input line 6086.
Package hyperref Info: backreferencing OFF on input line 6093.
Package hyperref Info: Link coloring OFF on input line 6098.
Package hyperref Info: Link coloring with OCG OFF on input line 6103.
Package hyperref Info: PDF/A mode OFF on input line 6108.
\Hy@abspage=\count302
\c@Item=\count303
\c@Hfootnote=\count304
)
Package hyperref Info: Driver (autodetected): hpdftex.

(g:/texlive/2025/texmf-dist/tex/latex/hyperref/hpdftex.def
File: hpdftex.def 2025-07-12 v7.01o Hyperref driver for pdfTeX
\Fld@listcount=\count305
\c@bookmark@seq@number=\count306

(g:/texlive/2025/texmf-dist/tex/latex/rerunfilecheck/rerunfilecheck.sty
Package: rerunfilecheck 2025-06-21 v1.11 Rerun checks for auxiliary files (HO)

(g:/texlive/2025/texmf-dist/tex/generic/uniquecounter/uniquecounter.sty
Package: uniquecounter 2019/12/15 v1.4 Provide unlimited unique counter (HO)
)
Package uniquecounter Info: New unique counter `rerunfilecheck' on input line 2
84.
)
\Hy@SectionHShift=\skip54
)
(g:/texlive/2025/texmf-dist/tex/latex/l3backend/l3backend-pdftex.def
File: l3backend-pdftex.def 2025-06-09 L3 backend support: PDF output (pdfTeX)
\l__color_backend_stack_int=\count307
)
(./Mathematical_Model_LaTeX.aux)
\openout1 = `Mathematical_Model_LaTeX.aux'.

LaTeX Font Info:    Checking defaults for OML/cmm/m/it on input line 7.
LaTeX Font Info:    ... okay on input line 7.
LaTeX Font Info:    Checking defaults for OMS/cmsy/m/n on input line 7.
LaTeX Font Info:    ... okay on input line 7.
LaTeX Font Info:    Checking defaults for OT1/cmr/m/n on input line 7.
LaTeX Font Info:    ... okay on input line 7.
LaTeX Font Info:    Checking defaults for T1/cmr/m/n on input line 7.
LaTeX Font Info:    ... okay on input line 7.
LaTeX Font Info:    Checking defaults for TS1/cmr/m/n on input line 7.
LaTeX Font Info:    ... okay on input line 7.
LaTeX Font Info:    Checking defaults for OMX/cmex/m/n on input line 7.
LaTeX Font Info:    ... okay on input line 7.
LaTeX Font Info:    Checking defaults for U/cmr/m/n on input line 7.
LaTeX Font Info:    ... okay on input line 7.
LaTeX Font Info:    Checking defaults for PD1/pdf/m/n on input line 7.
LaTeX Font Info:    ... okay on input line 7.
LaTeX Font Info:    Checking defaults for PU/pdf/m/n on input line 7.
LaTeX Font Info:    ... okay on input line 7.
Package hyperref Info: Link coloring OFF on input line 7.
 (./Mathematical_Model_LaTeX.out)
(./Mathematical_Model_LaTeX.out)
\@outlinefile=\write3
\openout3 = `Mathematical_Model_LaTeX.out'.

LaTeX Font Info:    Trying to load font information for U+msa on input line 12.


(g:/texlive/2025/texmf-dist/tex/latex/amsfonts/umsa.fd
File: umsa.fd 2013/01/14 v3.01 AMS symbols A
)
LaTeX Font Info:    Trying to load font information for U+msb on input line 12.


(g:/texlive/2025/texmf-dist/tex/latex/amsfonts/umsb.fd
File: umsb.fd 2013/01/14 v3.01 AMS symbols B
)

[1

{g:/texlive/2025/texmf-var/fonts/map/pdftex/updmap/pdftex.map}{g:/texlive/2025/
texmf-dist/fonts/enc/dvips/cm-super/cm-super-ts1.enc}]

[2

] (./Mathematical_Model_LaTeX.aux)
 ***********
LaTeX2e <2025-06-01> patch level 1
L3 programming layer <2025-08-13>
 ***********
Package rerunfilecheck Info: File `Mathematical_Model_LaTeX.out' has not change
d.
(rerunfilecheck)             Checksum: D41D8CD98F00B204E9800998ECF8427E;0.
 ) 
Here is how much of TeX's memory you used:
 8763 strings out of 467800
 131706 string characters out of 5436061
 540365 words of memory out of 5000000
 37338 multiletter control sequences out of 15000+600000
 630069 words of font info for 53 fonts, out of 8000000 for 9000
 1141 hyphenation exceptions out of 8191
 75i,5n,79p,250b,444s stack positions out of 10000i,1000n,20000p,200000b,200000s
<g:/texlive/2025/texmf-dist/fonts/type1/public/amsfonts/cm/cmbx12.pfb><g:/tex
live/2025/texmf-dist/fonts/type1/public/amsfonts/cm/cmex10.pfb><g:/texlive/2025
/texmf-dist/fonts/type1/public/amsfonts/cm/cmmi10.pfb><g:/texlive/2025/texmf-di
st/fonts/type1/public/amsfonts/cm/cmmi7.pfb><g:/texlive/2025/texmf-dist/fonts/t
ype1/public/amsfonts/cm/cmr10.pfb><g:/texlive/2025/texmf-dist/fonts/type1/publi
c/amsfonts/cm/cmr7.pfb><g:/texlive/2025/texmf-dist/fonts/type1/public/amsfonts/
cm/cmsy10.pfb><g:/texlive/2025/texmf-dist/fonts/type1/public/amsfonts/cm/cmsy7.
pfb><g:/texlive/2025/texmf-dist/fonts/type1/public/cm-super/sfrm1000.pfb>
Output written on Mathematical_Model_LaTeX.pdf (2 pages, 96051 bytes).
PDF statistics:
 69 PDF objects out of 1000 (max. 8388607)
 46 compressed objects within 1 object stream
 7 named destinations out of 1000 (max. 500000)
 1 words of extra memory for PDF output out of 10000 (max. 10000000)

