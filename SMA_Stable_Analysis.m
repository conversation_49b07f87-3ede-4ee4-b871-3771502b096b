%% SMA双稳态梁稳定分析 - 数值稳定版本
% 解决方案：保守参数 + 稳定积分 + tanh滞回模型
% 重点：确保数值稳定性，避免发散

clear all; close all; clc;

%% 系统参数（调整为更稳定的值）
fprintf('=== SMA双稳态梁稳定分析 ===\n');

% 双稳态梁参数
a = 100.0;          % 势能系数 [N/m³] - 减小以提高稳定性
b = 10.0;           % 势能系数 [N/m]
m_eff = 0.01;       % 等效质量 [kg]
c = 0.1;            % 阻尼系数 [N·s/m] - 增大阻尼提高稳定性

% SMA弹簧参数
H = 0.1;            % 弹簧安装高度 [m]
L_0 = 0.08;         % 弹簧初始长度 [m]
k_M = 50.0;         % 马氏体相刚度 [N/m]
k_A = 200.0;        % 奥氏体相刚度 [N/m] - 减小刚度差异

% 相变温度 [°C]
M_s = 50;  M_f = 40;
A_s = 60;  A_f = 70;

% 仿真参数
dt = 0.005;         % 更小的时间步长确保稳定性
t_total = 15;       % 总时间 [s]
T_period = 7.5;     % 温度周期 [s]
T_base = 55;        % 基础温度 [°C]
T_amp = 20;         % 温度振幅 [°C] - 减小振幅

fprintf('调整后参数: a=%.0f, 稳定点=±%.1fmm, 阻尼=%.2f\n', a, sqrt(b/a)*1000, c);
fprintf('SMA刚度: %.0f-%.0fN/m, 温度范围: %.0f-%.0f°C\n', k_M, k_A, T_base-T_amp, T_base+T_amp);

%% 稳定的动态仿真
fprintf('\n正在进行稳定动态仿真...\n');

% 时间向量
t = 0:dt:t_total;
n_steps = length(t);

% 预分配
q = zeros(size(t));
qd = zeros(size(t));
T_history = zeros(size(t));

% 初始条件
q(1) = -sqrt(b/a) * 0.9;  % 稍微偏离稳定点
qd(1) = 0;

% 温度函数
temp_func = @(t) T_base + T_amp * sin(2*pi*t/T_period);

% 稳定积分循环
tic;
T_prev = temp_func(0) - 1;

for i = 1:n_steps-1
    % 当前状态
    q_curr = q(i);
    qd_curr = qd(i);
    t_curr = t(i);
    
    % 计算温度
    T_curr = temp_func(t_curr);
    T_history(i) = T_curr;
    
    % SMA刚度（保持tanh模型）
    k_curr = calculate_SMA_stiffness_hysteresis(T_curr, T_prev, M_s, M_f, A_s, A_f, k_M, k_A);
    
    % 计算力
    F_bistable = -a*q_curr^3 + b*q_curr;
    F_SMA = -k_curr*(q_curr - (H - L_0));
    F_damping = -c*qd_curr;
    
    % 加速度
    qdd_curr = (F_bistable + F_SMA + F_damping) / m_eff;
    
    % 稳定性检查
    if abs(qdd_curr) > 100  % 加速度限制
        qdd_curr = sign(qdd_curr) * 100;
    end
    
    % 改进的积分方法（Verlet积分）
    if i == 1
        q(i+1) = q_curr + qd_curr * dt + 0.5 * qdd_curr * dt^2;
        qd(i+1) = qd_curr + qdd_curr * dt;
    else
        q_new = 2*q_curr - q(i-1) + qdd_curr * dt^2;
        qd_new = (q_new - q(i-1)) / (2*dt);
        
        % 稳定性检查
        if abs(q_new) > 0.5 || abs(qd_new) > 10
            % 回退到欧拉方法
            q(i+1) = q_curr + qd_curr * dt;
            qd(i+1) = qd_curr + qdd_curr * dt;
        else
            q(i+1) = q_new;
            qd(i+1) = qd_new;
        end
    end
    
    T_prev = T_curr;
end

T_history(end) = temp_func(t(end));
solve_time = toc;

fprintf('仿真完成，用时: %.3f秒，时间步数: %d\n', solve_time, n_steps);

% 检查数值稳定性
if any(~isfinite(q)) || any(~isfinite(qd))
    fprintf('警告: 检测到数值不稳定，请调整参数\n');
    return;
end

%% Snap-Through检测
fprintf('正在检测Snap-Through事件...\n');

% 计算加速度
qdd = gradient(qd, dt);

% 检测标准
velocity_threshold = 0.01;
displacement_threshold = 0.005;

% 简化的事件检测
high_velocity_indices = find(abs(qd) > velocity_threshold);

snap_events = [];
if ~isempty(high_velocity_indices)
    % 分组连续事件
    event_starts = [];
    event_ends = [];
    
    if length(high_velocity_indices) > 0
        current_start = high_velocity_indices(1);
        
        for i = 2:length(high_velocity_indices)
            if high_velocity_indices(i) - high_velocity_indices(i-1) > 10
                % 新事件开始
                event_starts = [event_starts, current_start];
                event_ends = [event_ends, high_velocity_indices(i-1)];
                current_start = high_velocity_indices(i);
            end
        end
        event_starts = [event_starts, current_start];
        event_ends = [event_ends, high_velocity_indices(end)];
    end
    
    % 验证事件
    for i = 1:length(event_starts)
        start_idx = max(1, event_starts(i) - 5);
        end_idx = min(length(t), event_ends(i) + 5);
        
        displacement_change = abs(q(end_idx) - q(start_idx));
        max_velocity = max(abs(qd(start_idx:end_idx)));
        
        if displacement_change > displacement_threshold
            snap_events = [snap_events; [start_idx, end_idx, t(start_idx), t(end_idx), ...
                          q(start_idx), q(end_idx), T_history(start_idx), ...
                          max_velocity, displacement_change]];
        end
    end
end

fprintf('检测到 %d 个Snap-Through事件\n', size(snap_events,1));

%% 可视化
fprintf('正在生成图表...\n');

figure('Name', 'SMA双稳态梁稳定分析', 'Position', [100 100 1200 800]);

% 位移-温度轨迹
subplot(2,2,1);
plot(T_history, q*1000, 'b-', 'LineWidth', 1.5);
hold on;
if ~isempty(snap_events)
    for i = 1:size(snap_events,1)
        event_indices = snap_events(i,1):snap_events(i,2);
        plot(T_history(event_indices), q(event_indices)*1000, 'r-', 'LineWidth', 3);
    end
end
xlabel('温度 [°C]');
ylabel('位移 [mm]');
title('位移-温度轨迹');
grid on;

% 时间历程
subplot(2,2,2);
yyaxis left;
plot(t, q*1000, 'b-', 'LineWidth', 1.5);
ylabel('位移 [mm]');
yyaxis right;
plot(t, T_history, 'r-', 'LineWidth', 1.5);
ylabel('温度 [°C]');
xlabel('时间 [s]');
title('时间历程');
grid on;

% 速度历程
subplot(2,2,3);
plot(t, qd, 'g-', 'LineWidth', 1.5);
hold on;
if ~isempty(snap_events)
    for i = 1:size(snap_events,1)
        event_indices = snap_events(i,1):snap_events(i,2);
        plot(t(event_indices), qd(event_indices), 'r-', 'LineWidth', 3);
    end
end
xlabel('时间 [s]');
ylabel('速度 [m/s]');
title('速度历程');
grid on;

% 相空间
subplot(2,2,4);
plot(q*1000, qd, 'b-', 'LineWidth', 1);
xlabel('位移 [mm]');
ylabel('速度 [m/s]');
title('相空间轨迹');
grid on;

% 保存结果
if ~exist('Results', 'dir'), mkdir('Results'); end
saveas(gcf, fullfile('Results', 'SMA_Stable_Analysis.png'));

%% 输出结果
fprintf('\n=== 稳定分析结果 ===\n');
fprintf('计算性能: 用时%.3f秒, %d时间步\n', solve_time, n_steps);
fprintf('位移范围: %.2f - %.2f mm\n', min(q)*1000, max(q)*1000);
fprintf('最大速度: %.4f m/s\n', max(abs(qd)));

if ~isempty(snap_events)
    fprintf('\nSnap-Through事件:\n');
    for i = 1:size(snap_events,1)
        fprintf('事件%d: t=%.2fs, 位移变化=%.2fmm, 最大速度=%.4fm/s, 温度=%.1f°C\n', ...
                i, snap_events(i,3), snap_events(i,9)*1000, snap_events(i,8), snap_events(i,7));
    end
else
    fprintf('\n当前参数下未检测到snap-through事件\n');
    fprintf('系统表现为连续的非线性响应\n');
end

fprintf('\n分析完成！数值稳定，计算快速。\n');

%% 辅助函数（保持原有tanh模型）
function k = calculate_SMA_stiffness_hysteresis(T_current, T_previous, M_s, M_f, A_s, A_f, k_M, k_A)
    if T_current >= T_previous
        xi_A = calculate_austenite_fraction(T_current, A_s, A_f);
        k = k_M + (k_A - k_M) * xi_A;
    else
        xi_M = calculate_martensite_fraction(T_current, M_s, M_f);
        k = k_A - (k_A - k_M) * xi_M;
    end
    k = max(min(k_M, k_A), min(max(k_M, k_A), k));
end

function xi_A = calculate_austenite_fraction(T, A_s, A_f)
    if A_f <= A_s, xi_A = (T >= A_s); return; end
    T_mid = (A_s + A_f) / 2;
    T_width = (A_f - A_s) / 4;
    xi_A = 0.5 * (1 + tanh((T - T_mid) / T_width));
    xi_A = max(0, min(1, xi_A));
end

function xi_M = calculate_martensite_fraction(T, M_s, M_f)
    if M_s <= M_f, xi_M = (T <= M_s); return; end
    T_mid = (M_s + M_f) / 2;
    T_width = (M_s - M_f) / 4;
    xi_M = 0.5 * (1 + tanh((T_mid - T) / T_width));
    xi_M = max(0, min(1, xi_M));
end
