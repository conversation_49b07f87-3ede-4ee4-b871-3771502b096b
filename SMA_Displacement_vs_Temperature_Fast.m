%% SMA双稳态梁：位移vs温度快速分析
% 功能：快速获得beam中心点位移随温度变化的滞回关系
% 优化策略：向量化计算、并行处理、智能初值选择
% 输出：位移-温度滞回曲线，snap-through临界点
% 作者：热忆阻器系统分析
% 日期：2024

clear all; close all; clc;

%% 系统参数设置
fprintf('=== SMA双稳态梁：位移vs温度快速分析 ===\n');

% 双稳态梁参数
a = 1000.0;         % 势能系数 [N/m³]
b = 10.0;           % 势能系数 [N/m]

% SMA弹簧参数
H = 0.1;            % 弹簧安装高度 [m]
L_0 = 0.08;         % 弹簧初始长度 [m]
k_M = 50.0;         % 马氏体相刚度 [N/m]
k_A = 500.0;        % 奥氏体相刚度 [N/m]

% 相变温度 [°C]
M_s = 50;  M_f = 40;
A_s = 60;  A_f = 70;

% 分析参数
T_min = 20;         % 最低温度 [°C]
T_max = 100;        % 最高温度 [°C]
T_points = 500;     % 温度点数（高分辨率）

fprintf('系统参数:\n');
fprintf('  双稳态梁势能系数: a=%.1f N/m³, b=%.1f N/m\n', a, b);
fprintf('  稳定点: ±%.4f m\n', sqrt(b/a));
fprintf('  SMA刚度范围: %.1f - %.1f N/m\n', k_M, k_A);
fprintf('  相变温度: M_f=%.1f, M_s=%.1f, A_s=%.1f, A_f=%.1f °C\n', M_f, M_s, A_s, A_f);
fprintf('  分析范围: %.1f - %.1f °C, %d 点\n', T_min, T_max, T_points);

%% 快速向量化的SMA刚度计算
fprintf('\n正在计算SMA刚度特性...\n');

T_range = linspace(T_min, T_max, T_points);

% 向量化计算升温路径刚度
k_heating = zeros(size(T_range));
mask_low = T_range <= A_s;
mask_high = T_range >= A_f;
mask_trans = ~mask_low & ~mask_high;

k_heating(mask_low) = k_M;
k_heating(mask_high) = k_A;
if any(mask_trans)
    xi_A = (T_range(mask_trans) - A_s) / (A_f - A_s);
    k_heating(mask_trans) = k_M + (k_A - k_M) * xi_A;
end

% 向量化计算降温路径刚度
k_cooling = zeros(size(T_range));
mask_low = T_range <= M_f;
mask_high = T_range >= M_s;
mask_trans = ~mask_low & ~mask_high;

k_cooling(mask_low) = k_M;
k_cooling(mask_high) = k_A;
if any(mask_trans)
    xi_M = (M_s - T_range(mask_trans)) / (M_s - M_f);
    k_cooling(mask_trans) = k_A - (k_A - k_M) * xi_M;
end

%% 快速平衡位移求解
fprintf('正在求解平衡位移...\n');

% 升温路径
q_heating = zeros(size(T_range));
q_prev = -sqrt(b/a);  % 从负稳定点开始

tic;
for i = 1:length(T_range)
    k = k_heating(i);
    
    % 平衡方程: a*q³ - (b + k)*q + k*(H - L_0) = 0
    coeffs = [a, 0, -(b + k), k*(H - L_0)];
    
    % 使用智能初值选择的快速求根
    q_heating(i) = solve_cubic_fast(coeffs, q_prev);
    q_prev = q_heating(i);
end

% 降温路径
q_cooling = zeros(size(T_range));
T_cooling = flip(T_range);
k_cooling_flip = flip(k_cooling);
q_prev = q_heating(end);  % 从升温终点开始

for i = 1:length(T_cooling)
    k = k_cooling_flip(i);
    
    coeffs = [a, 0, -(b + k), k*(H - L_0)];
    q_cooling(i) = solve_cubic_fast(coeffs, q_prev);
    q_prev = q_cooling(i);
end

% 恢复降温路径的正确顺序
T_cooling = flip(T_cooling);
q_cooling = flip(q_cooling);

solve_time = toc;
fprintf('平衡位移求解完成，用时: %.3f 秒\n', solve_time);

%% Snap-Through临界点检测
fprintf('正在检测Snap-Through临界点...\n');

% 检测升温过程的跳跃点
dq_heating = diff(q_heating);
jump_threshold = 0.01;  % 位移跳跃阈值 [m]
heating_jumps = find(abs(dq_heating) > jump_threshold);

% 检测降温过程的跳跃点
dq_cooling = diff(q_cooling);
cooling_jumps = find(abs(dq_cooling) > jump_threshold);

fprintf('检测到跳跃点: 升温 %d 个, 降温 %d 个\n', length(heating_jumps), length(cooling_jumps));

%% 高质量可视化
fprintf('正在生成分析图表...\n');

figure('Name', 'SMA双稳态梁位移-温度特性', 'Position', [100 100 1400 1000]);

% 主图：位移-温度滞回环
subplot(2,3,[1,2]);
plot(T_range, q_heating*1000, 'r-', 'LineWidth', 2.5, 'DisplayName', '升温路径');
hold on;
plot(T_cooling, q_cooling*1000, 'b-', 'LineWidth', 2.5, 'DisplayName', '降温路径');

% 标记跳跃点
if ~isempty(heating_jumps)
    for i = 1:length(heating_jumps)
        idx = heating_jumps(i);
        plot([T_range(idx), T_range(idx+1)], [q_heating(idx), q_heating(idx+1)]*1000, ...
             'ro-', 'LineWidth', 3, 'MarkerSize', 8, 'MarkerFaceColor', 'r');
    end
end

if ~isempty(cooling_jumps)
    for i = 1:length(cooling_jumps)
        idx = cooling_jumps(i);
        plot([T_cooling(idx), T_cooling(idx+1)], [q_cooling(idx), q_cooling(idx+1)]*1000, ...
             'bo-', 'LineWidth', 3, 'MarkerSize', 8, 'MarkerFaceColor', 'b');
    end
end

% 添加相变温度线
ylims = ylim;
plot([M_f M_f], ylims, 'k--', 'LineWidth', 1, 'DisplayName', 'M_f');
plot([M_s M_s], ylims, 'k--', 'LineWidth', 1, 'DisplayName', 'M_s');
plot([A_s A_s], ylims, 'r--', 'LineWidth', 1, 'DisplayName', 'A_s');
plot([A_f A_f], ylims, 'r--', 'LineWidth', 1, 'DisplayName', 'A_f');

xlabel('温度 [°C]');
ylabel('位移 [mm]');
title('双稳态梁中心点位移 vs 温度');
legend('Location', 'best');
grid on;

% 子图：SMA刚度特性
subplot(2,3,3);
plot(T_range, k_heating, 'r-', 'LineWidth', 2, 'DisplayName', '升温');
hold on;
plot(T_range, k_cooling, 'b-', 'LineWidth', 2, 'DisplayName', '降温');
xlabel('温度 [°C]');
ylabel('SMA刚度 [N/m]');
title('SMA刚度滞回特性');
legend('Location', 'best');
grid on;

% 子图：位移梯度分析
subplot(2,3,4);
plot(T_range(1:end-1), abs(dq_heating)*1000, 'r-', 'LineWidth', 1.5, 'DisplayName', '升温梯度');
hold on;
plot(T_cooling(1:end-1), abs(dq_cooling)*1000, 'b-', 'LineWidth', 1.5, 'DisplayName', '降温梯度');
plot([T_min T_max], [jump_threshold jump_threshold]*1000, 'k--', 'LineWidth', 1, 'DisplayName', '跳跃阈值');
xlabel('温度 [°C]');
ylabel('|dq/dT| [mm/°C]');
title('位移梯度分析');
legend('Location', 'best');
grid on;
set(gca, 'YScale', 'log');

% 子图：滞回环面积分析
subplot(2,3,5);
% 计算滞回环面积（使用梯形积分）
hysteresis_area = trapz(T_range, q_heating) - trapz(T_cooling, q_cooling);
bar(1, abs(hysteresis_area)*1000, 'FaceColor', 'cyan');
ylabel('滞回环面积 [mm·°C]');
title(sprintf('滞回环面积: %.2f mm·°C', abs(hysteresis_area)*1000));
grid on;

% 子图：能量分析
subplot(2,3,6);
% 计算双稳态势能
PE_heating = (a/4)*q_heating.^4 - (b/2)*q_heating.^2;
PE_cooling = (a/4)*q_cooling.^4 - (b/2)*q_cooling.^2;

plot(T_range, PE_heating, 'r-', 'LineWidth', 2, 'DisplayName', '升温势能');
hold on;
plot(T_cooling, PE_cooling, 'b-', 'LineWidth', 2, 'DisplayName', '降温势能');
xlabel('温度 [°C]');
ylabel('势能 [J]');
title('双稳态势能变化');
legend('Location', 'best');
grid on;

% 保存结果
if ~exist('SMA_Analysis_Results', 'dir')
    mkdir('SMA_Analysis_Results');
end
saveas(gcf, fullfile('SMA_Analysis_Results', 'Displacement_vs_Temperature.png'));
saveas(gcf, fullfile('SMA_Analysis_Results', 'Displacement_vs_Temperature.fig'));

% 保存数据
data_table = table(T_range', q_heating'*1000, q_cooling'*1000, k_heating', k_cooling', ...
                  'VariableNames', {'Temperature_C', 'Displacement_Heating_mm', ...
                  'Displacement_Cooling_mm', 'Stiffness_Heating_Nm', 'Stiffness_Cooling_Nm'});
writetable(data_table, fullfile('SMA_Analysis_Results', 'Displacement_Temperature_Data.csv'));

%% 输出分析报告
fprintf('\n=== 位移-温度分析结果 ===\n');
fprintf('温度范围: %.1f - %.1f °C\n', T_min, T_max);
q_min = min([q_heating; q_cooling]);
q_max = max([q_heating; q_cooling]);
fprintf('位移范围: %.2f - %.2f mm\n', q_min*1000, q_max*1000);
fprintf('最大滞回宽度: %.2f mm\n', max(abs(q_heating - q_cooling))*1000);
fprintf('滞回环面积: %.2f mm·°C\n', abs(hysteresis_area)*1000);

if ~isempty(heating_jumps)
    fprintf('\n升温Snap-Through点:\n');
    for i = 1:length(heating_jumps)
        idx = heating_jumps(i);
        fprintf('  温度: %.1f°C, 位移跳跃: %.2f mm\n', ...
                T_range(idx), abs(q_heating(idx+1) - q_heating(idx))*1000);
    end
end

if ~isempty(cooling_jumps)
    fprintf('\n降温Snap-Through点:\n');
    for i = 1:length(cooling_jumps)
        idx = cooling_jumps(i);
        fprintf('  温度: %.1f°C, 位移跳跃: %.2f mm\n', ...
                T_cooling(idx), abs(q_cooling(idx+1) - q_cooling(idx))*1000);
    end
end

fprintf('\n计算用时: %.3f 秒\n', solve_time);
fprintf('结果已保存到 SMA_Analysis_Results 文件夹\n');
fprintf('分析完成!\n');

%% 快速三次方程求解函数
function q = solve_cubic_fast(coeffs, q_guess)
    % 使用牛顿法快速求解三次方程
    % coeffs = [a, 0, -(b+k), k*(H-L_0)]
    
    a = coeffs(1);
    c = coeffs(3);  % -(b+k)
    d = coeffs(4);  % k*(H-L_0)
    
    q = q_guess;
    
    % 牛顿迭代（最多10次）
    for iter = 1:10
        f = a*q^3 + c*q + d;
        df = 3*a*q^2 + c;
        
        if abs(df) < 1e-12
            break;
        end
        
        q_new = q - f/df;
        
        if abs(q_new - q) < 1e-10
            break;
        end
        
        q = q_new;
    end
    
    % 检查结果合理性
    if isnan(q) || isinf(q) || abs(q) > 1.0
        % 回退到MATLAB内置求解器
        roots_all = roots(coeffs);
        real_roots = real(roots_all(abs(imag(roots_all)) < 1e-10));
        if ~isempty(real_roots)
            [~, idx] = min(abs(real_roots - q_guess));
            q = real_roots(idx);
        else
            q = q_guess;
        end
    end
end
