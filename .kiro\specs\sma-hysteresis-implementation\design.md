# SMA滞回效应实现设计文档

## 概述

本设计文档描述了如何将现有的无状态SMA刚度模型升级为有状态的滞回模型。核心思想是实现路径依赖的本构关系，使系统能够区分升温和降温过程，从而产生真实的"回"字形滞回曲线。

## 架构

### 核心组件架构

```
SMA滞回系统
├── k_SMA_Hysteresis (核心滞回函数)
│   ├── 路径判断逻辑
│   ├── 升温路径计算
│   └── 降温路径计算
├── 准静态分析模块
│   ├── 加热过程循环
│   ├── 冷却过程循环
│   └── 根选择优化
└── 动态仿真集成
    ├── Persistent状态管理
    └── ODE兼容性接口
```

### 数据流架构

```mermaid
graph TD
    A[当前温度T] --> B[温度历史T_prev]
    B --> C{路径判断}
    C -->|升温| D[奥氏体路径]
    C -->|降温| E[马氏体路径]
    D --> F[计算奥氏体分数]
    E --> G[计算马氏体分数]
    F --> H[刚度计算k]
    G --> H
    H --> I[平衡位移求解]
    I --> J[根选择优化]
    J --> K[输出位移q]
```

## 组件和接口

### 1. k_SMA_Hysteresis 函数

**接口定义:**
```matlab
function k = k_SMA_Hysteresis(T_current, T_previous)
```

**输入参数:**
- `T_current`: 当前温度 [°C]
- `T_previous`: 前一时刻温度 [°C]

**输出:**
- `k`: SMA刚度 [N/m]

**内部逻辑:**

1. **路径判断模块:**
   ```matlab
   if T_current >= T_previous
       path = 'heating';  % 升温路径
   else
       path = 'cooling';  % 降温路径
   end
   ```

2. **升温路径计算:**
   - 使用奥氏体相变温度 (As, Af)
   - 计算奥氏体分数: `austenite_fraction = f(T, As, Af)`
   - 刚度插值: `k = k_M + (k_A - k_M) * austenite_fraction`

3. **降温路径计算:**
   - 使用马氏体相变温度 (Ms, Mf)
   - 计算马氏体分数: `martensite_fraction = f(T, Ms, Mf)`
   - 刚度插值: `k = k_A - (k_A - k_M) * martensite_fraction`

### 2. 相变分数计算函数

**奥氏体分数计算:**
```matlab
function xi_A = calculate_austenite_fraction(T, A_s, A_f)
    T_mid = (A_s + A_f) / 2;
    T_width = (A_f - A_s) / 4;
    xi_A = 0.5 * (1 + tanh((T - T_mid) / T_width));
end
```

**马氏体分数计算:**
```matlab
function xi_M = calculate_martensite_fraction(T, M_s, M_f)
    T_mid = (M_s + M_f) / 2;
    T_width = (M_s - M_f) / 4;  % 注意：降温过程
    xi_M = 0.5 * (1 + tanh((T_mid - T) / T_width));
end
```

### 3. 准静态分析接口

**修改后的 plot_sma_displacement_temperature 函数:**

```matlab
function plot_sma_displacement_temperature()
    % 加热过程
    T_heating = linspace(20, 100, 100);
    q_heating = zeros(size(T_heating));
    T_prev = T_heating(1) - 1;  % 初始化为升温状态
    
    for i = 1:length(T_heating)
        k = k_SMA_Hysteresis(T_heating(i), T_prev);
        q_heating(i) = solve_equilibrium(k);
        T_prev = T_heating(i);
    end
    
    % 冷却过程
    T_cooling = linspace(100, 20, 100);
    q_cooling = zeros(size(T_cooling));
    T_prev = T_cooling(1) + 1;  % 初始化为降温状态
    
    for i = 1:length(T_cooling)
        k = k_SMA_Hysteresis(T_cooling(i), T_prev);
        q_cooling(i) = solve_equilibrium(k);
        T_prev = T_cooling(i);
    end
end
```

### 4. 根选择优化接口

```matlab
function q_selected = select_physical_root(roots_array, q_previous)
    % 选择最接近前一步位移的实数根
    real_roots = real(roots_array(abs(imag(roots_array)) < 1e-10));
    if ~isempty(real_roots)
        [~, idx] = min(abs(real_roots - q_previous));
        q_selected = real_roots(idx);
    else
        q_selected = q_previous;  % 保持连续性
    end
end
```

## 数据模型

### SMA状态数据结构

```matlab
% 全局SMA状态变量（用于动态仿真）
persistent sma_state;
if isempty(sma_state)
    sma_state = struct(...
        'T_last', 25.0, ...           % 上一时刻温度
        'path', 'heating', ...        % 当前路径状态
        'xi_A', 0.0, ...             % 奥氏体分数
        'xi_M', 1.0 ...              % 马氏体分数
    );
end
```

### 相变参数结构

```matlab
% SMA材料参数
sma_params = struct(...
    'k_M', 50.0, ...      % 马氏体刚度 [N/m]
    'k_A', 500.0, ...     % 奥氏体刚度 [N/m]
    'M_s', 50, ...        % 马氏体开始温度 [°C]
    'M_f', 40, ...        % 马氏体结束温度 [°C]
    'A_s', 60, ...        % 奥氏体开始温度 [°C]
    'A_f', 70 ...         % 奥氏体结束温度 [°C]
);
```

## 错误处理

### 1. 参数验证

```matlab
function validate_sma_parameters(M_f, M_s, A_s, A_f)
    if ~(M_f < M_s && M_s < A_s && A_s < A_f)
        error('SMA参数错误: 必须满足 M_f < M_s < A_s < A_f');
    end
    
    if (M_s - M_f) < 1 || (A_f - A_s) < 1
        warning('相变温度范围过小，可能导致数值不稳定');
    end
end
```

### 2. 数值稳定性处理

```matlab
function k = ensure_stiffness_bounds(k, k_M, k_A)
    % 确保刚度在物理范围内
    k = max(min(k, max(k_M, k_A)), min(k_M, k_A));
end
```

### 3. 根求解错误处理

```matlab
function q = robust_equilibrium_solver(coeffs, q_prev)
    try
        roots_complex = roots(coeffs);
        q = select_physical_root(roots_complex, q_prev);
    catch ME
        warning('根求解失败，使用前一步结果: %s', ME.message);
        q = q_prev;
    end
end
```

## 测试策略

### 1. 单元测试

**k_SMA_Hysteresis 函数测试:**
- 测试升温路径：验证使用As, Af参数
- 测试降温路径：验证使用Ms, Mf参数
- 测试边界条件：极端温度值
- 测试连续性：温度变化方向切换

**相变分数计算测试:**
- 验证分数范围 [0, 1]
- 验证平滑过渡特性
- 验证边界值正确性

### 2. 集成测试

**准静态分析测试:**
- 验证滞回环闭合性
- 验证升温/降温路径分离
- 验证snap-through现象

**动态仿真测试:**
- 验证persistent状态管理
- 验证ODE求解器兼容性
- 验证长时间仿真稳定性

### 3. 物理验证测试

**滞回特性验证:**
- 对比参考文献数据
- 验证滞回环面积
- 验证相变温度响应

**能量守恒测试:**
- 验证系统总能量
- 验证耗散特性
- 验证稳定性分析

## 性能考虑

### 1. 计算效率优化

- 使用查找表加速tanh计算
- 缓存重复的根求解结果
- 优化循环中的内存分配

### 2. 数值精度控制

- 使用适当的容差设置
- 避免除零和数值溢出
- 实现自适应精度控制

### 3. 内存管理

- 合理使用persistent变量
- 及时清理临时数组
- 优化大规模数据处理

## 实现优先级

### 第一阶段：核心滞回功能
1. 实现 k_SMA_Hysteresis 函数
2. 实现相变分数计算
3. 修改准静态分析函数

### 第二阶段：优化和集成
1. 实现根选择优化
2. 集成动态仿真支持
3. 添加错误处理机制

### 第三阶段：验证和完善
1. 实现测试套件
2. 性能优化
3. 文档和示例完善

这个设计确保了SMA滞回模型的物理准确性、数值稳定性和计算效率，为实现真实的"回"字形滞回曲线提供了完整的技术方案。