%% SMA双稳态梁稳健分析 - 解决计算时间过长问题
% 策略：使用固定时间步长 + 显式积分 + tanh滞回模型
% 目标：快速且稳定的snap-through分析

clear all; close all; clc;

%% 系统参数
fprintf('=== SMA双稳态梁稳健分析 ===\n');

% 双稳态梁参数
a = 1000.0;         % 势能系数 [N/m³]
b = 10.0;           % 势能系数 [N/m]
m_eff = 0.01;       % 等效质量 [kg]
c = 0.05;           % 阻尼系数 [N·s/m]

% SMA弹簧参数
H = 0.1;            % 弹簧安装高度 [m]
L_0 = 0.08;         % 弹簧初始长度 [m]
k_M = 50.0;         % 马氏体相刚度 [N/m]
k_A = 500.0;        % 奥氏体相刚度 [N/m]

% 相变温度 [°C]
M_s = 50;  M_f = 40;
A_s = 60;  A_f = 70;

% 仿真参数
dt = 0.01;          % 固定时间步长 [s]
t_total = 20;       % 总时间 [s]
T_period = 10;      % 温度周期 [s]
T_base = 55;        % 基础温度 [°C]
T_amp = 25;         % 温度振幅 [°C]

fprintf('系统参数: a=%.0f, b=%.0f, 稳定点=±%.1fmm\n', a, b, sqrt(b/a)*1000);
fprintf('仿真参数: dt=%.3fs, 总时间=%.0fs, 温度周期=%.0fs\n', dt, t_total, T_period);

%% 使用固定步长的显式积分方法
fprintf('\n正在进行固定步长动态仿真...\n');

% 时间向量
t = 0:dt:t_total;
n_steps = length(t);

% 预分配数组
q = zeros(size(t));
qd = zeros(size(t));
T_history = zeros(size(t));
k_history = zeros(size(t));

% 初始条件
q(1) = -sqrt(b/a);
qd(1) = 0;

% 温度函数
temp_func = @(t) T_base + T_amp * sin(2*pi*t/T_period);

% 显式积分循环
tic;
T_prev = temp_func(0) - 1;  % 初始温度历史

for i = 1:n_steps-1
    % 当前状态
    q_curr = q(i);
    qd_curr = qd(i);
    t_curr = t(i);
    
    % 计算当前温度
    T_curr = temp_func(t_curr);
    T_history(i) = T_curr;
    
    % 计算SMA刚度（保持tanh模型）
    k_curr = calculate_SMA_stiffness_hysteresis(T_curr, T_prev, M_s, M_f, A_s, A_f, k_M, k_A);
    k_history(i) = k_curr;
    
    % 计算力
    F_bistable = -a*q_curr^3 + b*q_curr;
    F_SMA = -k_curr*(q_curr - (H - L_0));
    F_damping = -c*qd_curr;
    
    % 计算加速度
    qdd_curr = (F_bistable + F_SMA + F_damping) / m_eff;
    
    % 显式欧拉积分
    q(i+1) = q_curr + qd_curr * dt;
    qd(i+1) = qd_curr + qdd_curr * dt;
    
    % 更新温度历史
    T_prev = T_curr;
    
    % 进度显示
    if mod(i, round(n_steps/10)) == 0
        fprintf('进度: %.0f%%\n', i/n_steps*100);
    end
end

% 处理最后一个点
T_history(end) = temp_func(t(end));
k_history(end) = k_history(end-1);

solve_time = toc;
fprintf('动态仿真完成，用时: %.2f秒，时间步数: %d\n', solve_time, n_steps);

%% 增强的Snap-Through检测
fprintf('\n正在检测Snap-Through事件...\n');

% 计算数值导数
qdd = gradient(qd, dt);  % 加速度

% 多重检测标准
velocity_threshold = 0.02;      % 速度阈值
acceleration_threshold = 2.0;   % 加速度阈值
displacement_threshold = 0.008; % 位移变化阈值

% 检测候选点
high_vel_mask = abs(qd) > velocity_threshold;
high_acc_mask = abs(qdd) > acceleration_threshold;
candidate_mask = high_vel_mask | high_acc_mask;

% 找到连续区域
candidate_diff = diff([0; candidate_mask(:); 0]);
start_indices = find(candidate_diff == 1);
end_indices = find(candidate_diff == -1) - 1;

% 验证snap-through事件
snap_events = [];
for i = 1:length(start_indices)
    start_idx = start_indices(i);
    end_idx = end_indices(i);
    
    % 扩展边界
    start_idx = max(1, start_idx - 5);
    end_idx = min(length(t), end_idx + 5);
    
    % 检查事件特征
    displacement_change = abs(q(end_idx) - q(start_idx));
    max_velocity = max(abs(qd(start_idx:end_idx)));
    duration = t(end_idx) - t(start_idx);
    
    % 验证条件
    if displacement_change > displacement_threshold && duration < 2.0
        [~, max_vel_idx] = max(abs(qd(start_idx:end_idx)));
        max_vel_idx = start_idx + max_vel_idx - 1;
        
        snap_events = [snap_events; [start_idx, end_idx, t(start_idx), t(end_idx), ...
                      q(start_idx), q(end_idx), T_history(max_vel_idx), ...
                      max_velocity, displacement_change, duration]];
    end
end

fprintf('检测到 %d 个Snap-Through事件\n', size(snap_events,1));

%% 可视化结果
fprintf('\n正在生成分析图表...\n');

figure('Name', 'SMA双稳态梁稳健分析结果', 'Position', [100 100 1400 1000]);

% 子图1: 位移-温度轨迹
subplot(2,3,1);
plot(T_history, q*1000, 'b-', 'LineWidth', 1.5, 'DisplayName', '动态轨迹');
hold on;
if ~isempty(snap_events)
    for i = 1:size(snap_events,1)
        event_indices = snap_events(i,1):snap_events(i,2);
        plot(T_history(event_indices), q(event_indices)*1000, 'r-', 'LineWidth', 3);
        plot(T_history(snap_events(i,1)), q(snap_events(i,1))*1000, 'ro', ...
             'MarkerSize', 8, 'MarkerFaceColor', 'r');
    end
end
xlabel('温度 [°C]');
ylabel('位移 [mm]');
title('位移-温度轨迹 + Snap-Through');
grid on;

% 子图2: 时间历程
subplot(2,3,2);
yyaxis left;
plot(t, q*1000, 'b-', 'LineWidth', 1.5);
ylabel('位移 [mm]');
yyaxis right;
plot(t, T_history, 'r-', 'LineWidth', 1.5);
ylabel('温度 [°C]');
xlabel('时间 [s]');
title('时间历程');
grid on;

% 子图3: 速度历程
subplot(2,3,3);
plot(t, qd, 'g-', 'LineWidth', 1.5);
hold on;
plot([0 t_total], [velocity_threshold velocity_threshold], 'r--', 'LineWidth', 1);
plot([0 t_total], [-velocity_threshold -velocity_threshold], 'r--', 'LineWidth', 1);
if ~isempty(snap_events)
    for i = 1:size(snap_events,1)
        event_indices = snap_events(i,1):snap_events(i,2);
        plot(t(event_indices), qd(event_indices), 'r-', 'LineWidth', 3);
    end
end
xlabel('时间 [s]');
ylabel('速度 [m/s]');
title('速度历程 + Snap-Through检测');
grid on;

% 子图4: 加速度历程
subplot(2,3,4);
plot(t, qdd, 'm-', 'LineWidth', 1.5);
hold on;
plot([0 t_total], [acceleration_threshold acceleration_threshold], 'r--', 'LineWidth', 1);
plot([0 t_total], [-acceleration_threshold -acceleration_threshold], 'r--', 'LineWidth', 1);
xlabel('时间 [s]');
ylabel('加速度 [m/s²]');
title('加速度历程');
grid on;

% 子图5: 相空间
subplot(2,3,5);
plot(q*1000, qd, 'b-', 'LineWidth', 1);
hold on;
if ~isempty(snap_events)
    for i = 1:size(snap_events,1)
        event_indices = snap_events(i,1):snap_events(i,2);
        plot(q(event_indices)*1000, qd(event_indices), 'r-', 'LineWidth', 3);
    end
end
xlabel('位移 [mm]');
ylabel('速度 [m/s]');
title('相空间轨迹');
grid on;

% 子图6: SMA刚度历程
subplot(2,3,6);
plot(t, k_history, 'orange', 'LineWidth', 1.5);
xlabel('时间 [s]');
ylabel('SMA刚度 [N/m]');
title('SMA刚度变化（tanh模型）');
grid on;

% 保存结果
if ~exist('Results', 'dir')
    mkdir('Results');
end
saveas(gcf, fullfile('Results', 'SMA_Robust_Analysis.png'));

%% 输出详细结果
fprintf('\n=== 稳健分析结果 ===\n');
fprintf('计算性能:\n');
fprintf('  仿真用时: %.2f秒\n', solve_time);
fprintf('  时间步数: %d\n', n_steps);
fprintf('  平均步长: %.4f秒\n', dt);

fprintf('系统响应:\n');
fprintf('  位移范围: %.2f - %.2f mm\n', min(q)*1000, max(q)*1000);
fprintf('  最大速度: %.3f m/s\n', max(abs(qd)));
fprintf('  最大加速度: %.1f m/s²\n', max(abs(qdd)));

if ~isempty(snap_events)
    fprintf('\nSnap-Through事件详情:\n');
    fprintf('编号  时间[s]    持续[s]   位移变化[mm]  最大速度[m/s]  温度[°C]\n');
    for i = 1:size(snap_events,1)
        fprintf('%2d    %.2f       %.3f     %8.2f     %8.3f      %6.1f\n', ...
                i, snap_events(i,3), snap_events(i,10), ...
                snap_events(i,9)*1000, snap_events(i,8), snap_events(i,7));
    end
else
    fprintf('\n未检测到明显的Snap-Through事件\n');
    fprintf('建议：调整温度范围或系统参数以触发snap-through\n');
end

fprintf('\n分析完成！\n');

%% 辅助函数（保持原有tanh模型）
function k = calculate_SMA_stiffness_hysteresis(T_current, T_previous, M_s, M_f, A_s, A_f, k_M, k_A)
    if T_current >= T_previous
        xi_A = calculate_austenite_fraction(T_current, A_s, A_f);
        k = k_M + (k_A - k_M) * xi_A;
    else
        xi_M = calculate_martensite_fraction(T_current, M_s, M_f);
        k = k_A - (k_A - k_M) * xi_M;
    end
    k = max(min(k_M, k_A), min(max(k_M, k_A), k));
end

function xi_A = calculate_austenite_fraction(T, A_s, A_f)
    if A_f <= A_s
        xi_A = (T >= A_s);
        return;
    end
    T_mid = (A_s + A_f) / 2;
    T_width = (A_f - A_s) / 4;
    xi_A = 0.5 * (1 + tanh((T - T_mid) / T_width));
    xi_A = max(0, min(1, xi_A));
end

function xi_M = calculate_martensite_fraction(T, M_s, M_f)
    if M_s <= M_f
        xi_M = (T <= M_s);
        return;
    end
    T_mid = (M_s + M_f) / 2;
    T_width = (M_s - M_f) / 4;
    xi_M = 0.5 * (1 + tanh((T_mid - T) / T_width));
    xi_M = max(0, min(1, xi_M));
end
