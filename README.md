# Modeling-of-a-magnetic-actuated-bistable-curved-beam

Here are supplementary Matlab codes for the iterative algorithm, which can calculate the multimodal deformation of the bistable curved beam under magnetic field actuation.

It contains Codes for:
1. Symbol operation during the theoretical modeling process, providing the expressions of magnetic torques, energies, and two stationary conditions;
2. Multimodal deformation analysis, which can plot the deformation under various directions of magnetic fields;
3. Calculation of critical angle for snapping and the range for S-Shape, which utilizes a smaller step size of theta_B for more accuracy. 



Title:
Modeling of the magneto-thermomechanically triggered multimodal transformations of bistable curved beam induced by magnetic torques

Author:
<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Jaehyung Ju

UM-SJTU Joint Institute, Shanghai Jiao Tong University, 800 Dongchuan Road, Shanghai 200240, China

