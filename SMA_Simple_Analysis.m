%% SMA双稳态梁简化分析 - 专注于位移vs温度
% 功能：快速获得beam中心点位移随温度变化的滞回关系
% 重点：静态分析 + 简化动态分析
% 输出：位移-温度滞回曲线

clear all; close all; clc;

%% 系统参数
fprintf('=== SMA双稳态梁位移-温度分析 ===\n');

% 双稳态梁参数
a = 1000.0;         % 势能系数 [N/m³]
b = 10.0;           % 势能系数 [N/m]

% SMA弹簧参数
H = 0.1;            % 弹簧安装高度 [m]
L_0 = 0.08;         % 弹簧初始长度 [m]
k_M = 50.0;         % 马氏体相刚度 [N/m]
k_A = 500.0;        % 奥氏体相刚度 [N/m]

% 相变温度 [°C]
M_s = 50;  M_f = 40;
A_s = 60;  A_f = 70;

fprintf('双稳态梁: a=%.0f N/m³, b=%.0f N/m\n', a, b);
fprintf('稳定点: ±%.1f mm\n', sqrt(b/a)*1000);
fprintf('SMA刚度范围: %.0f - %.0f N/m\n', k_M, k_A);
fprintf('相变温度: M_f=%.0f, M_s=%.0f, A_s=%.0f, A_f=%.0f °C\n', M_f, M_s, A_s, A_f);

%% 静态滞回分析
fprintf('\n正在计算静态位移-温度滞回关系...\n');

T_range = linspace(20, 100, 400);  % 高分辨率温度范围
q_heating = zeros(size(T_range));
q_cooling = zeros(size(T_range));

tic;

% 升温路径
q_prev = -sqrt(b/a);  % 从负稳定点开始
for i = 1:length(T_range)
    T = T_range(i);
    
    % SMA刚度计算（升温路径）
    if T <= A_s
        k = k_M;
    elseif T >= A_f
        k = k_A;
    else
        % 线性插值
        k = k_M + (k_A - k_M) * (T - A_s) / (A_f - A_s);
    end
    
    % 求解平衡方程: a*q³ - (b + k)*q + k*(H - L_0) = 0
    coeffs = [a, 0, -(b + k), k*(H - L_0)];
    q_heating(i) = solve_cubic_fast(coeffs, q_prev);
    q_prev = q_heating(i);
end

% 降温路径
T_cool = flip(T_range);
q_prev = q_heating(end);  % 从升温终点开始
for i = 1:length(T_cool)
    T = T_cool(i);
    
    % SMA刚度计算（降温路径）
    if T >= M_s
        k = k_A;
    elseif T <= M_f
        k = k_M;
    else
        % 线性插值
        k = k_A - (k_A - k_M) * (M_s - T) / (M_s - M_f);
    end
    
    coeffs = [a, 0, -(b + k), k*(H - L_0)];
    q_cooling(i) = solve_cubic_fast(coeffs, q_prev);
    q_prev = q_cooling(i);
end

% 恢复降温路径的正确顺序
T_cool = flip(T_cool);
q_cooling = flip(q_cooling);

calc_time = toc;
fprintf('静态分析完成，用时: %.3f 秒\n', calc_time);

%% 检测snap-through点
fprintf('正在检测snap-through临界点...\n');

% 检测升温过程的跳跃
dq_heating = diff(q_heating);
jump_threshold = 0.005;  % 位移跳跃阈值 [m]
heating_jumps = find(abs(dq_heating) > jump_threshold);

% 检测降温过程的跳跃
dq_cooling = diff(q_cooling);
cooling_jumps = find(abs(dq_cooling) > jump_threshold);

fprintf('检测到跳跃点: 升温 %d 个, 降温 %d 个\n', length(heating_jumps), length(cooling_jumps));

%% 计算关键指标
q_min = min([q_heating; q_cooling]);
q_max = max([q_heating; q_cooling]);
hysteresis_width = max(abs(q_heating - q_cooling));
hysteresis_area = trapz(T_range, q_heating) - trapz(T_cool, q_cooling);

%% 可视化结果
fprintf('正在生成分析图表...\n');

figure('Name', 'SMA双稳态梁位移-温度分析', 'Position', [100 100 1200 800]);

% 主图：位移-温度滞回环
subplot(2,2,1);
plot(T_range, q_heating*1000, 'r-', 'LineWidth', 2.5, 'DisplayName', '升温路径');
hold on;
plot(T_cool, q_cooling*1000, 'b-', 'LineWidth', 2.5, 'DisplayName', '降温路径');

% 标记跳跃点
if ~isempty(heating_jumps)
    for i = 1:length(heating_jumps)
        idx = heating_jumps(i);
        plot([T_range(idx), T_range(idx+1)], [q_heating(idx), q_heating(idx+1)]*1000, ...
             'ro-', 'LineWidth', 3, 'MarkerSize', 8, 'MarkerFaceColor', 'r');
    end
end

if ~isempty(cooling_jumps)
    for i = 1:length(cooling_jumps)
        idx = cooling_jumps(i);
        plot([T_cool(idx), T_cool(idx+1)], [q_cooling(idx), q_cooling(idx+1)]*1000, ...
             'bo-', 'LineWidth', 3, 'MarkerSize', 8, 'MarkerFaceColor', 'b');
    end
end

% 添加相变温度线
ylims = ylim;
plot([M_f M_f], ylims, 'k--', 'LineWidth', 1, 'DisplayName', 'M_f');
plot([M_s M_s], ylims, 'k--', 'LineWidth', 1, 'DisplayName', 'M_s');
plot([A_s A_s], ylims, 'r--', 'LineWidth', 1, 'DisplayName', 'A_s');
plot([A_f A_f], ylims, 'r--', 'LineWidth', 1, 'DisplayName', 'A_f');

xlabel('温度 [°C]');
ylabel('位移 [mm]');
title('双稳态梁中心点位移 vs 温度');
legend('Location', 'best');
grid on;

% 子图2：SMA刚度特性
subplot(2,2,2);
k_heating_plot = zeros(size(T_range));
k_cooling_plot = zeros(size(T_range));

for i = 1:length(T_range)
    T = T_range(i);
    if T <= A_s
        k_heating_plot(i) = k_M;
    elseif T >= A_f
        k_heating_plot(i) = k_A;
    else
        k_heating_plot(i) = k_M + (k_A - k_M) * (T - A_s) / (A_f - A_s);
    end
    
    if T >= M_s
        k_cooling_plot(i) = k_A;
    elseif T <= M_f
        k_cooling_plot(i) = k_M;
    else
        k_cooling_plot(i) = k_A - (k_A - k_M) * (M_s - T) / (M_s - M_f);
    end
end

plot(T_range, k_heating_plot, 'r-', 'LineWidth', 2, 'DisplayName', '升温');
hold on;
plot(T_range, k_cooling_plot, 'b-', 'LineWidth', 2, 'DisplayName', '降温');
xlabel('温度 [°C]');
ylabel('SMA刚度 [N/m]');
title('SMA刚度滞回特性');
legend('Location', 'best');
grid on;

% 子图3：位移梯度分析
subplot(2,2,3);
plot(T_range(1:end-1), abs(dq_heating)*1000, 'r-', 'LineWidth', 1.5, 'DisplayName', '升温梯度');
hold on;
plot(T_cool(1:end-1), abs(dq_cooling)*1000, 'b-', 'LineWidth', 1.5, 'DisplayName', '降温梯度');
plot([20 100], [jump_threshold jump_threshold]*1000, 'k--', 'LineWidth', 1, 'DisplayName', '跳跃阈值');
xlabel('温度 [°C]');
ylabel('|dq/dT| [mm/°C]');
title('位移梯度分析');
legend('Location', 'best');
grid on;
set(gca, 'YScale', 'log');

% 子图4：关键指标总结
subplot(2,2,4);
metrics = [q_min*1000, q_max*1000, hysteresis_width*1000, abs(hysteresis_area)*10];
metric_names = {'最小位移', '最大位移', '滞回宽度', '滞回面积×10'};
bar(metrics, 'FaceColor', 'cyan');
set(gca, 'XTickLabel', metric_names);
ylabel('数值 [mm 或 mm·°C×10]');
title('关键指标总结');
grid on;

% 保存结果
if ~exist('Results', 'dir')
    mkdir('Results');
end
saveas(gcf, fullfile('Results', 'SMA_Displacement_Temperature.png'));
saveas(gcf, fullfile('Results', 'SMA_Displacement_Temperature.fig'));

% 保存数据
data_table = table(T_range', q_heating'*1000, q_cooling'*1000, ...
                  'VariableNames', {'Temperature_C', 'Displacement_Heating_mm', 'Displacement_Cooling_mm'});
writetable(data_table, fullfile('Results', 'Displacement_Temperature_Data.csv'));

%% 输出分析报告
fprintf('\n=== 分析结果总结 ===\n');
fprintf('温度范围: %.1f - %.1f °C\n', min(T_range), max(T_range));
fprintf('位移范围: %.2f - %.2f mm\n', q_min*1000, q_max*1000);
fprintf('最大滞回宽度: %.2f mm\n', hysteresis_width*1000);
fprintf('滞回环面积: %.2f mm·°C\n', abs(hysteresis_area)*1000);

if ~isempty(heating_jumps)
    fprintf('\n升温Snap-Through点:\n');
    for i = 1:length(heating_jumps)
        idx = heating_jumps(i);
        fprintf('  温度: %.1f°C, 位移跳跃: %.2f mm\n', ...
                T_range(idx), abs(q_heating(idx+1) - q_heating(idx))*1000);
    end
end

if ~isempty(cooling_jumps)
    fprintf('\n降温Snap-Through点:\n');
    for i = 1:length(cooling_jumps)
        idx = cooling_jumps(i);
        fprintf('  温度: %.1f°C, 位移跳跃: %.2f mm\n', ...
                T_cool(idx), abs(q_cooling(idx+1) - q_cooling(idx))*1000);
    end
end

fprintf('\n计算用时: %.3f 秒\n', calc_time);
fprintf('结果已保存到 Results 文件夹\n');
fprintf('分析完成！\n');

%% 辅助函数
function q = solve_cubic_fast(coeffs, q_guess)
    % 快速三次方程求解
    a = coeffs(1);
    c = coeffs(3);  % -(b+k)
    d = coeffs(4);  % k*(H-L_0)
    
    q = q_guess;
    
    % 牛顿迭代法（最多5次）
    for iter = 1:5
        f = a*q^3 + c*q + d;
        df = 3*a*q^2 + c;
        
        if abs(df) < 1e-12 || abs(f) < 1e-12
            break;
        end
        
        q_new = q - f/df;
        
        if abs(q_new - q) < 1e-10
            break;
        end
        
        q = q_new;
    end
    
    % 检查结果合理性
    if isnan(q) || isinf(q) || abs(q) > 0.5
        % 回退到内置求解器
        roots_all = roots(coeffs);
        real_roots = real(roots_all(abs(imag(roots_all)) < 1e-10));
        if ~isempty(real_roots)
            [~, idx] = min(abs(real_roots - q_guess));
            q = real_roots(idx);
        else
            q = q_guess;
        end
    end
end
