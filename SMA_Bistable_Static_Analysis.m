%% SMA驱动的双稳态梁静态分析
% 功能：分析bistable beam中心点位移随温度变化的关系
% 包含：SMA滞回效应、静态平衡求解、PDE简化为代数方程
% 作者：热忆阻器系统分析
% 日期：2024

clear all; close all; clc;

%% 系统参数设置
fprintf('=== SMA-双稳态梁静态分析 ===\n');

% 双稳态梁参数
m_eff = 0.01;       % 等效质量 [kg]
c = 0.05;           % 阻尼系数 [N·s/m] (静态分析中不使用)
a = 1000.0;         % 势能系数 [N/m³] - 控制非线性刚度
b = 10.0;           % 势能系数 [N/m] - 线性刚度

% SMA弹簧参数
H = 0.1;            % 弹簧安装高度 [m]
L_0 = 0.08;         % 弹簧初始长度 [m]
k_M = 50.0;         % 马氏体相刚度 [N/m]
k_A = 500.0;        % 奥氏体相刚度 [N/m]

% 相变温度 [°C]
M_s = 50;  M_f = 40;    % 马氏体相变开始/结束温度
A_s = 60;  A_f = 70;    % 奥氏体相变开始/结束温度

fprintf('双稳态梁参数:\n');
fprintf('  等效质量: %.4f kg\n', m_eff);
fprintf('  势能系数: a=%.1f N/m³, b=%.1f N/m\n', a, b);
fprintf('  稳定平衡点: ±%.4f m\n', sqrt(b/a));

fprintf('SMA参数:\n');
fprintf('  安装高度: %.3f m\n', H);
fprintf('  初始长度: %.3f m\n', L_0);
fprintf('  刚度范围: %.1f - %.1f N/m\n', k_M, k_A);
fprintf('  相变温度: M_f=%.1f, M_s=%.1f, A_s=%.1f, A_f=%.1f °C\n', ...
        M_f, M_s, A_s, A_f);

%% 温度范围和步长设置
T_min = 20;         % 最低温度 [°C]
T_max = 100;        % 最高温度 [°C]
T_points = 200;     % 温度点数

% 创建温度向量
T_range = linspace(T_min, T_max, T_points);

%% 静态平衡分析

fprintf('\n正在进行静态平衡分析...\n');

%% 1. 无滞回的平衡分析（理想情况）
fprintf('1. 计算无滞回的理想平衡位移...\n');

q_ideal = zeros(size(T_range));
k_ideal = zeros(size(T_range));

for i = 1:length(T_range)
    T = T_range(i);
    
    % 计算SMA刚度（无滞回）
    k_ideal(i) = calculate_SMA_stiffness_ideal(T, A_s, A_f, k_M, k_A);
    
    % 求解静态平衡方程：a*q³ - (b + k)*q + k*(H - L_0) = 0
    coeffs = [a, 0, -(b + k_ideal(i)), k_ideal(i)*(H - L_0)];
    q_ideal(i) = solve_equilibrium_equation(coeffs, 0.05);
end

%% 2. 包含滞回的平衡分析（升温过程）
fprintf('2. 计算升温过程的滞回位移...\n');

q_heating = zeros(size(T_range));
k_heating = zeros(size(T_range));
T_prev = T_min - 5;  % 初始化为升温状态
q_prev = 0.05;       % 初始位移猜测

for i = 1:length(T_range)
    T = T_range(i);
    
    % 计算SMA刚度（考虑滞回，升温路径）
    k_heating(i) = calculate_SMA_stiffness_hysteresis(T, T_prev, ...
                    M_s, M_f, A_s, A_f, k_M, k_A);
    
    % 求解静态平衡方程
    coeffs = [a, 0, -(b + k_heating(i)), k_heating(i)*(H - L_0)];
    q_heating(i) = solve_equilibrium_equation(coeffs, q_prev);
    
    % 更新前一步状态
    T_prev = T;
    q_prev = q_heating(i);
end

%% 3. 包含滞回的平衡分析（降温过程）
fprintf('3. 计算降温过程的滞回位移...\n');

T_cooling = flip(T_range);  % 反向温度序列
q_cooling = zeros(size(T_cooling));
k_cooling = zeros(size(T_cooling));
T_prev = T_max + 5;  % 初始化为降温状态
q_prev = q_heating(end);  % 从升温结束位置开始

for i = 1:length(T_cooling)
    T = T_cooling(i);
    
    % 计算SMA刚度（考虑滞回，降温路径）
    k_cooling(i) = calculate_SMA_stiffness_hysteresis(T, T_prev, ...
                   M_s, M_f, A_s, A_f, k_M, k_A);
    
    % 求解静态平衡方程
    coeffs = [a, 0, -(b + k_cooling(i)), k_cooling(i)*(H - L_0)];
    q_cooling(i) = solve_equilibrium_equation(coeffs, q_prev);
    
    % 更新前一步状态
    T_prev = T;
    q_prev = q_cooling(i);
end

% 将降温数据重新排序以便绘图
T_cooling = flip(T_cooling);
q_cooling = flip(q_cooling);
k_cooling = flip(k_cooling);

%% 4. 分析系统特性
fprintf('\n分析系统特性:\n');

% 计算滞回环特征
hysteresis_width = max(abs(q_heating - q_cooling)) * 1000;  % mm
fprintf('最大滞回宽度: %.4f mm\n', hysteresis_width);

% 计算温度敏感性
dq_dT_heating = gradient(q_heating, T_range) * 1000;  % mm/°C
max_sensitivity = max(abs(dq_dT_heating));
fprintf('最大温度敏感性: %.6f mm/°C\n', max_sensitivity);

% 找到跳跃点（双稳态转换）
[jump_indices_heating] = find_bistable_jumps(q_heating);
[jump_indices_cooling] = find_bistable_jumps(q_cooling);

fprintf('升温过程双稳态跳跃点: %d 个\n', length(jump_indices_heating));
fprintf('降温过程双稳态跳跃点: %d 个\n', length(jump_indices_cooling));

%% 5. 计算SMA弹簧的应变和力
strain_heating = (H - q_heating - L_0) / L_0 * 100;  % 百分比应变
strain_cooling = (H - q_cooling - L_0) / L_0 * 100;

force_heating = k_heating .* (H - q_heating - L_0);  % SMA弹簧力
force_cooling = k_cooling .* (H - q_cooling - L_0);

%% 可视化结果
fprintf('\n正在生成可视化图表...\n');

% 创建图形窗口
figure('Name', 'SMA-双稳态梁静态分析', 'Position', [100 100 1400 900]);

% 子图1: 位移vs温度关系
subplot(2,3,1);
plot(T_range, q_ideal*1000, 'k--', 'LineWidth', 1.5, 'DisplayName', '理想（无滞回）');
hold on;
plot(T_range, q_heating*1000, 'r-', 'LineWidth', 2, 'DisplayName', '升温路径');
plot(T_cooling, q_cooling*1000, 'b-', 'LineWidth', 2, 'DisplayName', '降温路径');

% 标记相变温度
ylims = ylim;
plot([M_f M_f], ylims, 'k:', 'LineWidth', 1, 'DisplayName', 'M_f');
plot([M_s M_s], ylims, 'k:', 'LineWidth', 1, 'DisplayName', 'M_s');
plot([A_s A_s], ylims, 'g:', 'LineWidth', 1, 'DisplayName', 'A_s');
plot([A_f A_f], ylims, 'g:', 'LineWidth', 1, 'DisplayName', 'A_f');

xlabel('温度 [°C]');
ylabel('中心点位移 [mm]');
title('位移随温度变化关系');
legend('Location', 'best');
grid on;

% 子图2: SMA刚度vs温度
subplot(2,3,2);
plot(T_range, k_ideal, 'k--', 'LineWidth', 1.5, 'DisplayName', '理想刚度');
hold on;
plot(T_range, k_heating, 'r-', 'LineWidth', 2, 'DisplayName', '升温刚度');
plot(T_cooling, k_cooling, 'b-', 'LineWidth', 2, 'DisplayName', '降温刚度');

xlabel('温度 [°C]');
ylabel('SMA刚度 [N/m]');
title('SMA刚度随温度变化');
legend('Location', 'best');
grid on;

% 子图3: 滞回环
subplot(2,3,3);
plot(T_range, q_heating*1000, 'r-', 'LineWidth', 2);
hold on;
plot(T_cooling, q_cooling*1000, 'b-', 'LineWidth', 2);

% 添加方向箭头
mid_idx = round(length(T_range)/2);
quiver(T_range(mid_idx), q_heating(mid_idx)*1000, 5, 0, 'r', ...
       'LineWidth', 2, 'MaxHeadSize', 0.5);
quiver(T_cooling(mid_idx), q_cooling(mid_idx)*1000, -5, 0, 'b', ...
       'LineWidth', 2, 'MaxHeadSize', 0.5);

xlabel('温度 [°C]');
ylabel('位移 [mm]');
title('SMA滞回环');
legend('升温', '降温', 'Location', 'best');
grid on;

% 子图4: 温度敏感性
subplot(2,3,4);
plot(T_range, dq_dT_heating, 'r-', 'LineWidth', 2);
xlabel('温度 [°C]');
ylabel('温度敏感性 [mm/°C]');
title('位移的温度敏感性');
grid on;

% 标记最大敏感性点
[max_val, max_idx] = max(abs(dq_dT_heating));
hold on;
plot(T_range(max_idx), dq_dT_heating(max_idx), 'ro', 'MarkerSize', 8, ...
     'MarkerFaceColor', 'r');
text(T_range(max_idx), dq_dT_heating(max_idx), ...
     sprintf('  最大: %.4f mm/°C', max_val), 'FontSize', 10);

% 子图5: SMA应变
subplot(2,3,5);
plot(T_range, strain_heating, 'r-', 'LineWidth', 2, 'DisplayName', '升温应变');
hold on;
plot(T_cooling, strain_cooling, 'b-', 'LineWidth', 2, 'DisplayName', '降温应变');

xlabel('温度 [°C]');
ylabel('SMA应变 [%]');
title('SMA弹簧应变');
legend('Location', 'best');
grid on;

% 子图6: SMA弹簧力
subplot(2,3,6);
plot(T_range, force_heating, 'r-', 'LineWidth', 2, 'DisplayName', '升温力');
hold on;
plot(T_cooling, force_cooling, 'b-', 'LineWidth', 2, 'DisplayName', '降温力');

xlabel('温度 [°C]');
ylabel('SMA弹簧力 [N]');
title('SMA弹簧力');
legend('Location', 'best');
grid on;

% 保存图形
if ~exist('SMA_Static_Results', 'dir')
    mkdir('SMA_Static_Results');
end
saveas(gcf, fullfile('SMA_Static_Results', 'SMA_Bistable_Static_Analysis.png'));
saveas(gcf, fullfile('SMA_Static_Results', 'SMA_Bistable_Static_Analysis.fig'));

%% 详细分析图
figure('Name', '详细分析', 'Position', [200 200 1200 800]);

% 相变区域放大图
subplot(2,2,1);
T_zoom = T_range(T_range >= 35 & T_range <= 75);
q_heating_zoom = q_heating(T_range >= 35 & T_range <= 75);
q_cooling_zoom = q_cooling(T_range >= 35 & T_range <= 75);

plot(T_zoom, q_heating_zoom*1000, 'r-', 'LineWidth', 2, 'DisplayName', '升温');
hold on;
plot(T_zoom, q_cooling_zoom*1000, 'b-', 'LineWidth', 2, 'DisplayName', '降温');

% 标记相变温度
ylims = ylim;
plot([M_f M_f], ylims, 'k--', 'LineWidth', 1);
plot([M_s M_s], ylims, 'k--', 'LineWidth', 1);
plot([A_s A_s], ylims, 'g--', 'LineWidth', 1);
plot([A_f A_f], ylims, 'g--', 'LineWidth', 1);

xlabel('温度 [°C]');
ylabel('位移 [mm]');
title('相变区域详细图');
legend('Location', 'best');
grid on;

% 势能分析
subplot(2,2,2);
q_test = linspace(-0.1, 0.2, 1000);
T_test = [30, 50, 70, 90];
colors = {'b', 'g', 'r', 'm'};

for i = 1:length(T_test)
    k = calculate_SMA_stiffness_ideal(T_test(i), A_s, A_f, k_M, k_A);
    % 势能函数：U = (a/4)*q⁴ - (b+k)/2*q² + k*(H-L_0)*q
    U = (a/4)*q_test.^4 - (b+k)/2*q_test.^2 + k*(H-L_0)*q_test;
    plot(q_test*1000, U, colors{i}, 'LineWidth', 1.5, ...
         'DisplayName', sprintf('%.0f°C', T_test(i)));
    hold on;
end

xlabel('位移 [mm]');
ylabel('势能 [J]');
title('不同温度下的势能曲线');
legend('Location', 'best');
grid on;

% 双稳态区域分析
subplot(2,2,3);
bistable_temp_range = T_range(abs(q_heating) > 0.02);  % 双稳态阈值
bistable_displacement = q_heating(abs(q_heating) > 0.02) * 1000;

if ~isempty(bistable_temp_range)
    plot(bistable_temp_range, bistable_displacement, 'ro', 'MarkerSize', 4);
    xlabel('温度 [°C]');
    ylabel('双稳态位移 [mm]');
    title('双稳态工作区域');
    grid on;
    
    fprintf('双稳态工作温度范围: %.1f - %.1f °C\n', ...
            min(bistable_temp_range), max(bistable_temp_range));
end

% 滞回面积分析
subplot(2,2,4);
% 计算温度区间内的滞回面积
T_common = T_range;
q_heating_interp = interp1(T_range, q_heating, T_common, 'linear');
q_cooling_interp = interp1(T_cooling, q_cooling, T_common, 'linear');

hysteresis_area_local = cumtrapz(T_common, abs(q_heating_interp - q_cooling_interp));
plot(T_common, hysteresis_area_local*1000, 'k-', 'LineWidth', 2);
xlabel('温度 [°C]');
ylabel('累计滞回面积 [mm·°C]');
title('滞回面积积分');
grid on;

% 保存详细分析图
saveas(gcf, fullfile('SMA_Static_Results', 'SMA_Detailed_Analysis.png'));
saveas(gcf, fullfile('SMA_Static_Results', 'SMA_Detailed_Analysis.fig'));

%% 输出数据到文件
fprintf('\n正在保存数据到文件...\n');

% 创建数据表
data_table = table(T_range', q_ideal'*1000, q_heating'*1000, q_cooling'*1000, ...
                   k_ideal', k_heating', k_cooling', ...
                   strain_heating', strain_cooling', ...
                   force_heating', force_cooling', ...
                   'VariableNames', {'Temperature_C', 'Displacement_Ideal_mm', ...
                   'Displacement_Heating_mm', 'Displacement_Cooling_mm', ...
                   'Stiffness_Ideal_Nm', 'Stiffness_Heating_Nm', 'Stiffness_Cooling_Nm', ...
                   'Strain_Heating_percent', 'Strain_Cooling_percent', ...
                   'Force_Heating_N', 'Force_Cooling_N'});

% 保存为CSV文件
writetable(data_table, fullfile('SMA_Static_Results', 'SMA_Static_Analysis_Data.csv'));

% 保存参数信息
param_info = {
    '双稳态梁参数:';
    sprintf('等效质量: %.4f kg', m_eff);
    sprintf('势能系数: a=%.1f N/m³, b=%.1f N/m', a, b);
    sprintf('稳定平衡点: ±%.4f m', sqrt(b/a));
    '';
    'SMA参数:';
    sprintf('安装高度: %.3f m', H);
    sprintf('初始长度: %.3f m', L_0);
    sprintf('刚度范围: %.1f - %.1f N/m', k_M, k_A);
    sprintf('相变温度: M_f=%.1f, M_s=%.1f, A_s=%.1f, A_f=%.1f °C', M_f, M_s, A_s, A_f);
    '';
    '分析结果:';
    sprintf('最大滞回宽度: %.4f mm', hysteresis_width);
    sprintf('最大温度敏感性: %.6f mm/°C', max_sensitivity);
};

% 写入参数文件
fid = fopen(fullfile('SMA_Static_Results', 'Analysis_Parameters.txt'), 'w');
for i = 1:length(param_info)
    fprintf(fid, '%s\n', param_info{i});
end
fclose(fid);

fprintf('\n=== 分析完成 ===\n');
fprintf('结果已保存到 SMA_Static_Results 文件夹\n');
fprintf('包含以下文件:\n');
fprintf('1. SMA_Bistable_Static_Analysis.png/.fig - 主要分析图\n');
fprintf('2. SMA_Detailed_Analysis.png/.fig - 详细分析图\n');
fprintf('3. SMA_Static_Analysis_Data.csv - 数值数据\n');
fprintf('4. Analysis_Parameters.txt - 分析参数\n');

%% 函数定义部分

function k = calculate_SMA_stiffness_ideal(T, A_s, A_f, k_M, k_A)
    % 计算理想SMA刚度（无滞回）
    % 使用平滑tanh过渡
    
    T_mid = (A_s + A_f) / 2;
    T_width = (A_f - A_s) / 4;
    
    if T_width > 0
        k = k_M + (k_A - k_M) * 0.5 * (1 + tanh((T - T_mid) / T_width));
    else
        if T < T_mid
            k = k_M;
        else
            k = k_A;
        end
    end
end

function k = calculate_SMA_stiffness_hysteresis(T_current, T_previous, M_s, M_f, A_s, A_f, k_M, k_A)
    % 计算包含滞回的SMA刚度
    
    % 路径判断
    if T_current >= T_previous
        % 升温路径：使用奥氏体相变
        xi_A = calculate_austenite_fraction(T_current, A_s, A_f);
        k = k_M + (k_A - k_M) * xi_A;
    else
        % 降温路径：使用马氏体相变
        xi_M = calculate_martensite_fraction(T_current, M_s, M_f);
        k = k_A - (k_A - k_M) * xi_M;
    end
    
    % 确保刚度在合理范围内
    k = max(min(k_M, k_A), min(max(k_M, k_A), k));
end

function xi_A = calculate_austenite_fraction(T, A_s, A_f)
    % 计算奥氏体分数
    
    if A_f <= A_s
        if T >= A_s
            xi_A = 1.0;
        else
            xi_A = 0.0;
        end
        return;
    end
    
    T_mid = (A_s + A_f) / 2;
    T_width = (A_f - A_s) / 4;
    xi_A = 0.5 * (1 + tanh((T - T_mid) / T_width));
    xi_A = max(0, min(1, xi_A));
end

function xi_M = calculate_martensite_fraction(T, M_s, M_f)
    % 计算马氏体分数
    
    if M_s <= M_f
        if T <= M_s
            xi_M = 1.0;
        else
            xi_M = 0.0;
        end
        return;
    end
    
    T_mid = (M_s + M_f) / 2;
    T_width = (M_s - M_f) / 4;
    xi_M = 0.5 * (1 + tanh((T_mid - T) / T_width));
    xi_M = max(0, min(1, xi_M));
end

function q = solve_equilibrium_equation(coeffs, q_initial)
    % 求解静态平衡方程：a*q³ + 0*q² - (b+k)*q + k*(H-L_0) = 0
    
    try
        roots_complex = roots(coeffs);
        
        % 选择实数根
        real_roots = real(roots_complex(abs(imag(roots_complex)) < 1e-10));
        
        if isempty(real_roots)
            warning('未找到实数根，使用初始猜测值');
            q = q_initial;
            return;
        end
        
        % 选择最接近初始猜测的根
        [~, idx] = min(abs(real_roots - q_initial));
        q = real_roots(idx);
        
        % 检查结果合理性
        if isnan(q) || isinf(q) || abs(q) > 1.0
            warning('求解结果不合理，使用初始猜测值');
            q = q_initial;
        end
        
    catch ME
        warning('%s', ['根求解失败: ', ME.message]);
        q = q_initial;
    end
end

function jump_indices = find_bistable_jumps(displacement)
    % 找到双稳态跳跃点
    
    % 计算位移的梯度
    grad_q = gradient(displacement);
    
    % 找到梯度绝对值大于阈值的点
    threshold = 5 * std(grad_q);  % 动态阈值
    jump_indices = find(abs(grad_q) > threshold);
    
    % 合并相邻的跳跃点
    if length(jump_indices) > 1
        jump_diff = diff(jump_indices);
        jump_indices = jump_indices([true, jump_diff > 5]);  % 保持至少5个点的间隔
    end
end
