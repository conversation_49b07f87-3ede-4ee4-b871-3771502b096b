clear all;

%% Initial conditions and fixed parameters
dx=0.005;
E=200e9;
bb=1/100;
t=0.2/1000;
A=t*bb;
l=0.16;
h=0.02;
I=(t^3)*bb/12;
Q=h/t;
nModes=6;

x = linspace(0,l,(l/dx));
X=x/l;


%% Compute buckling loads Nj
Nj = zeros(1,nModes);
for j = 1:2:nModes
Nj(j) = pi*(j+1);
end
for j = 2:2:nModes
Nj(j) = calcNj(j)*pi;
end

%% Set up vectors to store Fourier coefficients for initial shape
% Cj=[];
% C0 = 1/2;
% Cj(1) = -1/2;
% Cj(2:nModes)=0;

Cj=zeros(nModes);
C0 = 0.6604;
Cj(1) = -0.3983;
Cj(2) = -0.1042;

%% Setup matrix for calculating F and delta for N parameter sweep

interval=0.05;
F_solve = {};
delta_solve = {};
delta1=[];
delta2=[];
Ns = [];

%% N parameter sweep



for q = 1:Nj(3)/interval

N = q*interval;
x = linspace(0,l,(l/dx));
Xi=x/l;
Ns(q) = N;

%% Calculate initial shape W_bar
W_bar = zeros(1, l/dx);
for k = 1:l/dx

W_bar(k)=(1-cos(Nj(1)*Xi(k)))*1/2;
end
s_bar=0;
w_bar=W_bar*h;

%% Calculate Force coefficients

SA = sumFuncA(N,Nj,nModes);
SAc = (3/(16*N^4))*(1-(tan(N/4)/(N/4))+(((tan(N/4))^2)/3));


SB = sumFuncB(N,Nj,nModes,Cj);
SBc = -1*(4*pi^2)/((N^2-(4*pi^2))^2);


SC = sumFuncC(N,Nj,nModes,Cj);
% SCc = -1*((pi^2)*(N^2)/4)*((N^2)-(8*(pi^2)))/(((N^2)-4*(pi^2))^2);
SCc = Nj(1)^2*N^2*(N^2-2*Nj(1)^2)/16;
SD = sumFuncD(N,Nj,nModes,Cj);

SE = sumFuncE(N,Nj,nModes);
SEc = sumFuncEc(N);

SA3 = sumFuncA(Nj(3),Nj,nModes);

SB3 = sumFuncB(Nj(3),Nj,nModes,Cj);

SC3 = sumFuncC(Nj(3),Nj,nModes,Cj);

SD3 = sumFuncD(Nj(3),Nj,nModes,Cj);

SE3 = sumFuncE(Nj(3),Nj,nModes);


%% Qmin for different Critical buckling Load Nc
Qmin=[];
for j=1:1:3
ScA = sumFuncA(Nj(j),Nj,nModes);
ScB = sumFuncB(Nj(j),Nj,nModes,Cj);
ScC = sumFuncC(Nj(j),Nj,nModes,Cj);
ScD = sumFuncD(Nj(j),Nj,nModes,Cj);
ScE = sumFuncE(Nj(j),Nj,nModes);

Qmin(j)=Nj(j)*sqrt(ScA/((3*(ScB^2))-12*ScA*ScC));

end


%% Solve for force F and delta for a given N
syms Fm

eqn = SAc*(Fm^2) + SB*Fm + SC + (N^2)/(12*(Q^2)) == 0;
Fsolve = solve(eqn,Fm);
Fs = double(Fsolve);

F_solve{q} = Fs;

delta1(q)=SD-(Fs(1)*SEc);
delta2(q)=SD-(Fs(2)*SEc);



%% Calculate Beam Shape W

% K coefficients

K=[];
if N<Nj(3)
for j = 1:4:nModes
K(j) = (4*Fs(1)+Cj(j)*N^2*Nj(j)^2)/((N^2-(Nj(j)^2))*Nj(j)^2);
end
for j = 3:4:nModes
K(j) = Cj(j)*N^2/(N^2-(Nj(j)^2));
end
for j = 2:2:nModes
K(j)=0;
end
elseif N==Nj(3)
for j = 1:4:nModes
K(j) = (4*Fs(1)+Cj(j)*Nj(3)^2*Nj(j)^2)/((Nj(3)^2-(Nj(j)^2))*Nj(j)^2);
end
for j = 7:4:nModes
K(j) = Cj(j)*Nj(3)^2/(Nj(3)^2-(Nj(j)^2));
end
for j = 2:2:nModes
K(j)=0;
end
K(3) = Cj(3)*Nj(3)^2/(Nj(3)^2-N^3);
end


% Calculate Wj from Eq 11

Wj={};

for j = 1:2:nModes
Wjj=[];
for k=1:(l/dx)
Wjj(k) = 1 - cos(Nj(j)*Xi(k));
end
Wj{j}=Wjj;
end


for j = 2:2:nModes
Wjj=[];
for k=1:1:(l/dx)
Wjj(k) = 1 - cos(Nj(j)*X(k)) - 2*Xi(k) + (2*sin(Nj(j)*Xi(k)))/Nj(j);
end
Wj{j}=Wjj;
end


%Calculate W shape function
W=0;
for p=1:1:nModes
Ww=[];
Ww=Wj{p};
W=W +K(p)*Ww;
end
W=W+W_bar;




%% Calculate Force and delta at different positions
% delta_top = SD3 + (32*(pi^2)*SB3/3) - sqrt((32*(pi^2)*SB3/3)^2 - (SC3/3) - (4*(pi)^2)/(9*Q^2));
delta_top = (28/27) - (2*pi/3) * sqrt((1/6) + (16/(81*pi^2)) - (1/(Q^2)));

% F_top = 64*pi^2*(((-32*pi^2*SB3)/3) + sqrt((32*pi^2*SB3/3)^2 - (SC3/3) - (4*pi^2)/(9*Q^2)));
F_top = 64*pi^2*((4/3)-delta_top);

% delta_bot = SD3 + (32*pi^2*SB3/3) + sqrt((32*pi^2*SB3/3)^2 - (SC3/3) - (4*pi^2)/(9*Q^2));
delta_bot = (28/27) + (2*pi/3) * sqrt((1/6) + (16/(81*pi^2)) - (1/(Q^2)));

% F_bot = 64*pi^2*((-32*pi^2*SB3/3) - sqrt((32*pi^2*SB3/3)^2 - (SC3/3) - (4*pi^2)/(9*Q^2)));
F_bot = 64*pi^2*((4/3)-delta_bot);



f_top=F_top*(E*I*h)/(l^3);
f_bot=F_bot*(E*I*h)/(l^3);

deltaf = [delta_top, delta_bot];
Force = [F_top, F_bot];


end

F1=[];
F2=[];
sizeF=length(F_solve);
for t=1:1:sizeF
Fo=cell2mat(F_solve(t));
Fa=Fo(1);
Fb=Fo(2);
F1(t)=Fa;
F2(t)=Fb;

end

% delta1=[];
% delta2=[];
% sized=length(delta_solve);
% for t=1:1:sized
% deltat=cell2mat(delta_solve(t));
% deltaa=deltat(1);
% deltab=deltat(2);
% delta1(t)=deltaa;
% delta2(t)=deltab;
% 
% end


d1 = delta1(delta1<1);
d2 = delta2(delta2<1);
d3 = delta1(delta1>1); 
d3 = sort (d3);
d4 = delta2(delta2>1);
d4 = sort (d4);

F11 = F1(1:length(d1));
F12 = F2((length(d4)+1):(length(d4)+length(d2)));

F13 = F1((length(d1)+1):(length(d1)+length(d3)));
F13 = sort (F13);
F14 = F2(1:length(d4));
F14 = sort (F14);




delta3=max(d2):(max(d1)-max(d2))/251:min(d3);
F3=64*pi^2*(SD3-delta3);
delta = [d1 d2 delta3 d3 d4];
F=[F11 F12 F3 F13 F14];


% delta = delta (delta<2);
% F = F (1:length(delta));


f=(F*E*I*h)/l^3;
f1=(F1*E*I*h)/l^3;
f2=(F2*E*I*h)/l^3;
f3=(F3*E*I*h)/l^3;
d = delta*h;

subplot(121);
% plot(delta1,f1);
% hold on
% plot(delta2,f2);
% plot(delta3,f3);
% hold off
plot(delta,F);

subplot(122);
% plot(delta1,Ns);
% hold on
% plot(delta2,Ns);
% hold off
plot(d,f);


% %% Plot force and delta
% % Ns=7.5:0.1:12.5;
% delta3=0:0.01:2;
% F3=64*pi^2*((4/3)-delta3);
% 
% 
% subplot(121);
% plot(delta1,F1);
% hold on
% plot(delta2,F2);
% plot(delta3,F3);
% hold off
% 
% subplot(122);
% plot(delta1,Ns);
% hold on
% plot(delta2,Ns);
% hold off







function nj = calcNj(j)
    
    % Define the periodic equation function
    eq = @(x) sin(x/2).*(tan(x/2)-(x/2));
    
    % Initial guess for fsolve
    x0 = (j+1)*pi-0.1*pi;
    
    % Solve the equation numerically
    options = optimset('Display','off');
    nj = fsolve(eq, x0, options);
    nj=nj/pi;
end
  
function S_a = sumFuncA(N,Nj,nModes)

S_a = 0;
for j = 1:4:nModes
S_a = S_a + (4*((N^2-Nj(1)^2)^2))/(((Nj(j))^2)*(((N^2)-Nj(j)^2)^(2)));
end

end

function S_b = sumFuncB(N,Nj,nModes,Cj)

S_b = 0;
for j = 1:4:nModes
S_b = S_b + 2*Cj(j)*(Nj(j)^2)/(((N^2)-Nj(j)^2)^(2));
end
end

function S_c = sumFuncC(N,Nj,nModes,Cj)
S_c=0;
for k = 1:2:nModes
Num = -1*(Cj(k)^2)*(Nj(k)^2)*(N^2)*((N^2)-(2*(Nj(k)^2)));
Den = 4*(((N^2)-(Nj(k)^2))^2);

S_c = S_c + Num/Den;
end
end

function S_d = sumFuncD(N,Nj,nModes,Cj)
S_d=0;
for j = 1:4:nModes
Num = -2*Cj(j)*(N^2);
Den = (N^2)-(Nj(j)^2);

S_d = S_d + Num/Den;
end
end

function S_e = sumFuncE(N,Nj,nModes)

S_e=0;
for j = 1:4:nModes
Num = 8;
Den = (Nj(j)^2)*((N^2)-(Nj(j)^2));

S_e = S_e + Num/Den;
end
end

function S_ec = sumFuncEc(N)

S_ec = (1/(N^3))*((N/4)-tan(N/4));

end
