clc
clear all
close all

% --- This file is the symbol operation for the simple bistable beam model
%     without magnetic field, where we obtain the expressions of mechanical 
%     force, energy, and stationary conditions for force-displacement analysis.
%    
% --- Eq1 and Eq2 are the two stationary conditions that need to be solved.
% --- F_expr is the expression of external force at beam center.
%
% --- Mathematical Model:
%     - Total potential energy: Pi = U_internal - W_external
%     - U_internal = Uc + Ub (compression energy + bending energy)
%     - W_external = F * delta (work done by external force)
%     - Force equilibrium: dPi/da1 = 0, dPi/da2 = 0
%     - Force calculation: F = dU_internal/d(delta) where delta = 2*a1

syms a1 a2 x L h pp EE b t II F delta

% Define mode shapes
y1 = a1*( 1-cos(2*pp*x/L)  );                                                % First mode (symmetric C-shape)
y2 = a2*( 1-2*x/L - cos( 2.86*pp*x/L ) + 2/2.86/pp*sin(2.86*pp*x/L )  );   % Second mode (antisymmetric S-shape)
y = y1+y2;                                                                   % Total displacement

% Calculate derivatives
dy = diff(y,x);     % First derivative
ddy = diff(y,x,2);  % Second derivative

% Initial configuration (undeformed state)
y0 = h/2*( 1-cos(2*pp*x/L)  );
dy0 = diff(y0,x);
ddy0 = diff(y0,x,2); 

% Calculate strain components
ds = int( ( 1/2*(  (dy)^2-(dy0)^2 ) ),x,0,L   );  % Extension strain
s0 = int( (1+1/2*(dy0)^2  ),x,0,L   );             % Initial arc length

% Calculate internal energies
p = -EE*b*t*ds/s0;                                  % Axial force
Uc = -p*ds;                                         % Compression energy
Ub = EE*II/2*int( (  ddy-ddy0  )^2, x,0,L  );      % Bending energy

% Total internal energy (strain energy)
U_internal = Uc + Ub;

% Calculate displacement at beam center (x = L/2)
% For mode shapes: delta = y(L/2) = a1*(1-cos(pi)) + a2*(...) ≈ 2*a1 (simplified)
% Using exact calculation for center displacement
x_center = L/2;
y1_center = subs(y1, x, x_center);
y2_center = subs(y2, x, x_center);
delta_center = y1_center + y2_center;

% Simplify delta_center expression
delta_center_simplified = simplify(delta_center);

% For equilibrium analysis, we use displacement control approach:
% Given a1 (displacement control), find corresponding a2 and force F

% Stationary conditions with respect to a1 and a2
dU_da1 = diff(U_internal, a1);  % This gives the generalized force F1 = 2*F (approximately)
dU_da2 = diff(U_internal, a2);  % This should equal zero for equilibrium

% The external force F can be calculated as:
% F = (1/2) * dU_internal/da1  (since delta ≈ 2*a1)
% More precisely: F = dU_internal/d(delta_center)

% For the equilibrium equations:
% Eq1: dU_internal/da1 = F * d(delta_center)/da1
% Eq2: dU_internal/da2 = 0 (internal equilibrium)

% Calculate the derivative of delta_center with respect to a1 and a2
ddelta_da1 = diff(delta_center, a1);
ddelta_da2 = diff(delta_center, a2);

% Display the key expressions
disp('=== Key Expressions for Force-Displacement Analysis ===')
disp(' ')
disp('1. Center displacement:')
disp('delta_center =')
disp(delta_center_simplified)
disp(' ')
disp('2. Derivative of center displacement:')
disp('ddelta_da1 =')
disp(ddelta_da1)
disp('ddelta_da2 =')
disp(ddelta_da2)

% Equilibrium equations:
% For displacement-controlled analysis:
% Eq1: dU_internal/da1 - F * ddelta_da1 = 0  =>  F = (dU_internal/da1) / ddelta_da1
% Eq2: dU_internal/da2 = 0

% Force expression (external force needed for given displacement)
F_expr = dU_da1 / ddelta_da1;

% Equilibrium equation for a2 (internal equilibrium)
Eq2 = dU_da2;

% Simplified force expression (using approximation delta ≈ 2*a1)
F_expr_simplified = dU_da1 / 2;

disp('3. Force expression (exact):')
disp('F = (dU_internal/da1) / (ddelta_center/da1) =')
disp(F_expr)
disp(' ')
disp('4. Force expression (simplified, delta ≈ 2*a1):')
disp('F ≈ (1/2) * (dU_internal/da1) =')
disp(F_expr_simplified)
disp(' ')
disp('5. Equilibrium equation for a2:')
disp('dU_internal/da2 = 0')
disp('Eq2 =')
disp(Eq2)

% Save the expressions for numerical computation
% These will be used in the main calculation script

% Export the key expressions
Eq1_force = dU_da1;      % This gives the generalized force component
Eq2_equilibrium = dU_da2; % This must equal zero

disp(' ')
disp('=== Summary ===')
disp('Eq1_force: Generalized force component (dU_internal/da1)')
disp('Eq2_equilibrium: Internal equilibrium condition (dU_internal/da2 = 0)')
disp('F_expr: External force = Eq1_force / ddelta_da1')
disp('F_expr_simplified: External force ≈ Eq1_force / 2')
disp(' ')
disp('For numerical analysis:')
disp('1. Given a1 (displacement control)')
disp('2. Solve Eq2_equilibrium = 0 for a2')
disp('3. Calculate F using F_expr or F_expr_simplified')
disp('4. Plot F vs delta_center for force-displacement curve')
