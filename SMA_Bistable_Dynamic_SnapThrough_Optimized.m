%% SMA驱动的双稳态梁动态Snap-Through分析 - 高效优化版本
% 功能：快速分析包含SMA滞回效应的动态snap-through过程
% 优化策略：预计算查找表、向量化操作、减少函数调用
% 作者：热忆阻器系统分析
% 日期：2024

clear all; close all; clc;

%% 系统参数设置
fprintf('=== SMA-双稳态梁动态Snap-Through分析 (优化版本) ===\n');

% 双稳态梁参数
m_eff = 0.01;       % 等效质量 [kg]
c = 0.05;           % 阻尼系数 [N·s/m]
a = 1000.0;         % 势能系数 [N/m³]
b = 10.0;           % 势能系数 [N/m]

% SMA弹簧参数
H = 0.1;            % 弹簧安装高度 [m]
L_0 = 0.08;         % 弹簧初始长度 [m]
k_M = 50.0;         % 马氏体相刚度 [N/m]
k_A = 500.0;        % 奥氏体相刚度 [N/m]

% 相变温度 [°C]
M_s = 50;  M_f = 40;
A_s = 60;  A_f = 70;

% 优化的仿真参数
t_total = 40;       % 总仿真时间 [s] - 2个完整周期
T_base = 55;        % 基础温度 [°C]
T_amplitude = 25;   % 温度振幅 [°C]
T_period = 20;      % 温度周期 [s]

fprintf('系统参数:\n');
fprintf('  双稳态梁: m=%.4f kg, c=%.3f N·s/m\n', m_eff, c);
fprintf('  势能系数: a=%.1f N/m³, b=%.1f N/m\n', a, b);
fprintf('  稳定点: ±%.4f m\n', sqrt(b/a));
fprintf('  SMA刚度范围: %.1f - %.1f N/m\n', k_M, k_A);
fprintf('  温度循环: %.1f ± %.1f °C, 周期 %.1f s\n', T_base, T_amplitude, T_period);

%% 预计算温度-刚度查找表（关键优化）
fprintf('\n正在预计算温度-刚度查找表...\n');

% 创建高分辨率的温度网格
T_lookup_min = T_base - T_amplitude - 5;
T_lookup_max = T_base + T_amplitude + 5;
T_lookup_points = 1000;  % 高分辨率查找表
T_lookup = linspace(T_lookup_min, T_lookup_max, T_lookup_points);

% 预计算升温和降温路径的刚度
k_heating_lookup = zeros(size(T_lookup));
k_cooling_lookup = zeros(size(T_lookup));

% 升温路径
for i = 1:length(T_lookup)
    T = T_lookup(i);
    if T <= A_s
        k_heating_lookup(i) = k_M;
    elseif T >= A_f
        k_heating_lookup(i) = k_A;
    else
        xi_A = (T - A_s) / (A_f - A_s);
        k_heating_lookup(i) = k_M + (k_A - k_M) * xi_A;
    end
end

% 降温路径
for i = 1:length(T_lookup)
    T = T_lookup(i);
    if T >= M_s
        k_cooling_lookup(i) = k_A;
    elseif T <= M_f
        k_cooling_lookup(i) = k_M;
    else
        xi_M = (M_s - T) / (M_s - M_f);
        k_cooling_lookup(i) = k_A - (k_A - k_M) * xi_M;
    end
end

fprintf('查找表创建完成: %d 个温度点\n', T_lookup_points);

%% 初始条件设置
q0 = -sqrt(b/a);    % 初始位移（负稳定点）
qd0 = 0;            % 初始速度
y0 = [q0; qd0];

fprintf('初始条件: q0=%.4f m, qd0=%.4f m/s\n', q0, qd0);

%% 温度函数定义
temperature_cycle = @(t) T_base + T_amplitude * sin(2*pi*t/T_period);

%% 主要动态仿真 - 使用优化的ODE函数
fprintf('\n正在进行动态仿真（优化版本）...\n');

% 优化的ODE求解器选项
options = odeset('RelTol', 1e-5, 'AbsTol', 1e-7, 'MaxStep', 0.1, ...
                'InitialStep', 0.01, 'Stats', 'on');

% 清理持久化变量
clear system_ode_optimized

% 求解动态方程
tic;
[t, y] = ode45(@(t,y) system_ode_optimized(t, y, m_eff, c, a, b, H, L_0, ...
               temperature_cycle, T_lookup, k_heating_lookup, k_cooling_lookup), ...
               [0, t_total], y0, options);
solve_time = toc;

fprintf('仿真完成，用时: %.2f 秒\n', solve_time);
fprintf('时间步数: %d\n', length(t));

% 提取结果
q = y(:,1);
qd = y(:,2);
T_history = arrayfun(temperature_cycle, t);

%% 快速Snap-Through事件检测
fprintf('正在检测Snap-Through事件...\n');

% 使用更高效的检测方法
velocity_threshold = 0.1;
displacement_change_threshold = 0.02;

% 找到高速度区域
high_velocity_mask = abs(qd) > velocity_threshold;
high_velocity_indices = find(high_velocity_mask);

% 检测snap-through事件
snap_events = [];
if ~isempty(high_velocity_indices)
    % 使用diff找到连续区域的边界
    velocity_diff = diff([0; high_velocity_mask; 0]);
    start_indices = find(velocity_diff == 1);
    end_indices = find(velocity_diff == -1) - 1;
    
    % 分析每个连续区域
    for i = 1:length(start_indices)
        start_idx = start_indices(i);
        end_idx = end_indices(i);
        
        % 检查位移变化幅度
        displacement_change = abs(q(end_idx) - q(start_idx));
        
        if displacement_change > displacement_change_threshold
            snap_events = [snap_events; [start_idx, end_idx, t(start_idx), t(end_idx), ...
                          q(start_idx), q(end_idx), T_history(start_idx)]];
        end
    end
end

fprintf('检测到 %d 个Snap-Through事件\n', size(snap_events,1));

%% 快速可视化
fprintf('正在生成分析图表...\n');

figure('Name', 'SMA双稳态梁动态分析-优化版本', 'Position', [100 100 1400 900]);

% 子图1: 位移-温度相图（核心结果）
subplot(2,3,1);
plot(T_history, q*1000, 'b-', 'LineWidth', 1.5);
hold on;

% 标记snap-through事件
for i = 1:size(snap_events,1)
    event_indices = snap_events(i,1):snap_events(i,2);
    plot(T_history(event_indices), q(event_indices)*1000, 'r-', 'LineWidth', 3);
    plot(T_history(snap_events(i,1)), q(snap_events(i,1))*1000, 'ro', ...
         'MarkerSize', 8, 'MarkerFaceColor', 'r');
end

xlabel('温度 [°C]');
ylabel('位移 [mm]');
title('位移-温度滞回轨迹');
grid on;
legend('动态轨迹', 'Snap-Through', '起点', 'Location', 'best');

% 子图2: 时间历程
subplot(2,3,2);
yyaxis left;
plot(t, q*1000, 'b-', 'LineWidth', 1.5);
ylabel('位移 [mm]');
yyaxis right;
plot(t, T_history, 'r-', 'LineWidth', 1.5);
ylabel('温度 [°C]');
xlabel('时间 [s]');
title('时间历程');
grid on;

% 子图3: 相空间轨迹
subplot(2,3,3);
plot(q*1000, qd, 'b-', 'LineWidth', 1);
hold on;
for i = 1:size(snap_events,1)
    event_indices = snap_events(i,1):snap_events(i,2);
    plot(q(event_indices)*1000, qd(event_indices), 'r-', 'LineWidth', 3);
end
xlabel('位移 [mm]');
ylabel('速度 [m/s]');
title('相空间轨迹');
grid on;

% 子图4: 速度历程
subplot(2,3,4);
plot(t, qd, 'c-', 'LineWidth', 1.5);
hold on;
plot([0 t_total], [velocity_threshold velocity_threshold], 'r--', 'LineWidth', 1);
plot([0 t_total], [-velocity_threshold -velocity_threshold], 'r--', 'LineWidth', 1);
xlabel('时间 [s]');
ylabel('速度 [m/s]');
title('速度历程');
grid on;

% 子图5: 能量分析
subplot(2,3,5);
KE = 0.5 * m_eff * qd.^2;
PE = (a/4)*q.^4 - (b/2)*q.^2;
plot(t, KE, 'r-', 'LineWidth', 1.5, 'DisplayName', '动能');
hold on;
plot(t, PE, 'b-', 'LineWidth', 1.5, 'DisplayName', '势能');
xlabel('时间 [s]');
ylabel('能量 [J]');
title('能量变化');
legend('Location', 'best');
grid on;

% 子图6: SMA应变
subplot(2,3,6);
SMA_strain = (H - q - L_0) / L_0 * 100;
plot(t, SMA_strain, 'purple', 'LineWidth', 1.5);
xlabel('时间 [s]');
ylabel('SMA应变 [%]');
title('SMA弹簧应变');
grid on;

% 保存结果
if ~exist('SMA_Dynamic_Results', 'dir')
    mkdir('SMA_Dynamic_Results');
end
saveas(gcf, fullfile('SMA_Dynamic_Results', 'SMA_Dynamic_Optimized.png'));

%% 输出分析报告
fprintf('\n=== 优化版本分析结果 ===\n');
fprintf('仿真时间: %.1f s，求解用时: %.2f s\n', t_total, solve_time);
fprintf('时间步数: %d，平均步长: %.4f s\n', length(t), t_total/length(t));
fprintf('检测到Snap-Through事件: %d 个\n', size(snap_events,1));

if ~isempty(snap_events)
    fprintf('\nSnap-Through事件详情:\n');
    for i = 1:size(snap_events,1)
        fprintf('事件 %d: t=%.3f-%.3fs, 位移变化=%.2fmm, 触发温度=%.1f°C\n', ...
                i, snap_events(i,3), snap_events(i,4), ...
                (snap_events(i,6)-snap_events(i,5))*1000, snap_events(i,7));
    end
end

fprintf('\n优化效果: 大幅提升求解速度，保持分析精度\n');
fprintf('分析完成!\n');

%% 优化的ODE函数定义
function dydt = system_ode_optimized(t, y, m_eff, c, a, b, H, L_0, ...
                                    temperature_cycle, T_lookup, k_heating_lookup, k_cooling_lookup)
    % 高效的动态系统ODE函数 - 使用预计算查找表
    
    persistent T_last is_initialized
    
    if isempty(is_initialized)
        T_last = temperature_cycle(0) - 1;
        is_initialized = true;
    end
    
    % 计算当前温度
    T_current = temperature_cycle(t);
    
    % 使用查找表快速获取SMA刚度
    if T_current >= T_last
        % 升温路径
        k = interp1(T_lookup, k_heating_lookup, T_current, 'linear', k_heating_lookup(end));
    else
        % 降温路径
        k = interp1(T_lookup, k_cooling_lookup, T_current, 'linear', k_cooling_lookup(1));
    end
    
    % 提取状态变量
    q = y(1);
    qd = y(2);
    
    % 计算力
    F_bistable = -a*q^3 + b*q;
    F_SMA = -k*(q - (H - L_0));
    F_damping = -c*qd;
    
    % 动力学方程
    qdd = (F_bistable + F_SMA + F_damping) / m_eff;
    
    dydt = [qd; qdd];
    
    T_last = T_current;
end
