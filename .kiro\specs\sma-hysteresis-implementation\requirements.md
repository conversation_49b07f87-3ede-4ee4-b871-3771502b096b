# SMA滞回效应实现需求文档

## 介绍

本文档定义了在SMA-双稳态梁系统中实现真实滞回效应的需求。当前的SMA模型是无状态的，无法产生"回"字形的滞回曲线。需要将模型升级为有状态的、路径依赖的本构模型，以正确模拟SMA材料在升温和降温过程中的不同相变路径。

## 需求

### 需求 1: 状态记忆功能

**用户故事:** 作为研究人员，我希望SMA模型能够记住温度变化的历史，以便正确模拟材料的路径依赖特性。

#### 验收标准

1. WHEN 系统计算SMA刚度时 THEN 模型 SHALL 能够区分当前是升温还是降温过程
2. WHEN 温度从低到高变化时 THEN 系统 SHALL 使用奥氏体相变路径（As, Af温度参数）
3. WHEN 温度从高到低变化时 THEN 系统 SHALL 使用马氏体相变路径（Ms, Mf温度参数）
4. WHEN 系统初始化时 THEN 模型 SHALL 正确设置初始温度状态

### 需求 2: 双路径相变模型

**用户故事:** 作为研究人员，我希望SMA模型使用不同的相变路径来模拟升温和降温过程，以便产生真实的滞回效应。

#### 验收标准

1. WHEN 系统处于升温路径时 THEN 模型 SHALL 基于奥氏体分数计算刚度
2. WHEN 系统处于降温路径时 THEN 模型 SHALL 基于马氏体分数计算刚度
3. WHEN 计算奥氏体分数时 THEN 系统 SHALL 使用As和Af温度参数
4. WHEN 计算马氏体分数时 THEN 系统 SHALL 使用Ms和Mf温度参数
5. WHEN 相变完成时 THEN 刚度值 SHALL 在kM和kA之间平滑过渡

### 需求 3: 准静态分析中的滞回曲线

**用户故事:** 作为研究人员，我希望在温度-位移图中看到闭合的"回"字形滞回曲线，以便验证SMA模型的正确性。

#### 验收标准

1. WHEN 执行准静态分析时 THEN 系统 SHALL 生成包含升温和降温两个不同路径的位移曲线
2. WHEN 绘制温度-位移图时 THEN 升温路径和降温路径 SHALL 明显分离形成滞回环
3. WHEN 分析完成时 THEN 滞回曲线 SHALL 是闭合的"回"字形
4. WHEN 发生snap-through现象时 THEN 升温和降温过程 SHALL 在不同温度点发生跳变

### 需求 4: 根选择优化

**用户故事:** 作为研究人员，我希望系统能够智能选择物理上合理的平衡位置，以便确保位移曲线的连续性。

#### 验收标准

1. WHEN 求解三次方程得到多个实数根时 THEN 系统 SHALL 选择最接近前一步位移的根
2. WHEN 计算平衡位移时 THEN 选择的根 SHALL 保证位移曲线的物理连续性
3. WHEN 处于snap-through区域时 THEN 根选择算法 SHALL 正确处理位移跳变

### 需求 5: 动态仿真兼容性

**用户故事:** 作为研究人员，我希望新的滞回模型能够与现有的动态仿真系统兼容，以便进行完整的系统分析。

#### 验收标准

1. WHEN 在动态仿真中使用滞回模型时 THEN 系统 SHALL 正确处理自适应时间步长
2. WHEN ODE求解器调用系统函数时 THEN 模型 SHALL 能够访问温度变化历史
3. WHEN 使用persistent变量时 THEN 系统 SHALL 正确维护温度状态记忆
4. IF 需要更严谨的实现 THEN 系统 SHALL 支持将SMA状态作为额外状态变量

### 需求 6: 参数配置和验证

**用户故事:** 作为研究人员，我希望能够配置SMA相变参数并验证滞回效应，以便确保模型的准确性。

#### 验收标准

1. WHEN 设置相变温度参数时 THEN 系统 SHALL 验证Ms < Mf < As < Af的物理约束
2. WHEN 运行分析时 THEN 系统 SHALL 输出滞回环的关键特征参数
3. WHEN 比较结果时 THEN 滞回曲线 SHALL 与参考文献中的实验数据一致
4. WHEN 参数不合理时 THEN 系统 SHALL 提供清晰的错误信息和建议