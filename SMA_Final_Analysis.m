%% SMA双稳态梁最终分析 - 快速稳定版本
% 解决方案：固定步长积分 + tanh滞回模型 + 智能snap-through检测
% 目标：快速获得位移vs温度关系 + 检测snap-through事件

clear all; close all; clc;

%% 系统参数
fprintf('=== SMA双稳态梁最终分析 ===\n');

% 双稳态梁参数
a = 1000.0;         % 势能系数 [N/m³]
b = 10.0;           % 势能系数 [N/m]
m_eff = 0.01;       % 等效质量 [kg]
c = 0.05;           % 阻尼系数 [N·s/m]

% SMA弹簧参数
H = 0.1;            % 弹簧安装高度 [m]
L_0 = 0.08;         % 弹簧初始长度 [m]
k_M = 50.0;         % 马氏体相刚度 [N/m]
k_A = 500.0;        % 奥氏体相刚度 [N/m]

% 相变温度 [°C]
M_s = 50;  M_f = 40;
A_s = 60;  A_f = 70;

% 仿真参数
dt = 0.01;          % 固定时间步长 [s]
t_total = 20;       % 总时间 [s]
T_period = 10;      % 温度周期 [s]
T_base = 55;        % 基础温度 [°C]
T_amp = 25;         % 温度振幅 [°C]

fprintf('系统参数: 稳定点=±%.1fmm, SMA刚度=%.0f-%.0fN/m\n', sqrt(b/a)*1000, k_M, k_A);
fprintf('仿真参数: dt=%.3fs, 总时间=%.0fs\n', dt, t_total);

%% 固定步长动态仿真
fprintf('\n正在进行动态仿真...\n');

% 时间向量和预分配
t = 0:dt:t_total;
n_steps = length(t);
q = zeros(size(t));
qd = zeros(size(t));
T_history = zeros(size(t));

% 初始条件
q(1) = -sqrt(b/a);
qd(1) = 0;

% 温度函数
temp_func = @(t) T_base + T_amp * sin(2*pi*t/T_period);

% 积分循环
tic;
T_prev = temp_func(0) - 1;

for i = 1:n_steps-1
    % 当前状态
    q_curr = q(i);
    qd_curr = qd(i);
    t_curr = t(i);
    
    % 计算温度
    T_curr = temp_func(t_curr);
    T_history(i) = T_curr;
    
    % SMA刚度（tanh模型）
    k_curr = calculate_SMA_stiffness_hysteresis(T_curr, T_prev, M_s, M_f, A_s, A_f, k_M, k_A);
    
    % 计算力
    F_bistable = -a*q_curr^3 + b*q_curr;
    F_SMA = -k_curr*(q_curr - (H - L_0));
    F_damping = -c*qd_curr;
    
    % 加速度
    qdd_curr = (F_bistable + F_SMA + F_damping) / m_eff;
    
    % 积分更新
    q(i+1) = q_curr + qd_curr * dt;
    qd(i+1) = qd_curr + qdd_curr * dt;
    
    T_prev = T_curr;
end

T_history(end) = temp_func(t(end));
solve_time = toc;

fprintf('仿真完成，用时: %.2f秒，时间步数: %d\n', solve_time, n_steps);

%% Snap-Through检测
fprintf('正在检测Snap-Through事件...\n');

% 计算加速度
qdd = gradient(qd, dt);

% 检测标准
velocity_threshold = 0.02;
acceleration_threshold = 2.0;
displacement_threshold = 0.008;

% 检测高动态活动
high_activity = (abs(qd) > velocity_threshold) | (abs(qdd) > acceleration_threshold);

% 找到事件边界
activity_diff = diff([0; double(high_activity(:)); 0]);
start_indices = find(activity_diff == 1);
end_indices = find(activity_diff == -1) - 1;

% 验证snap-through事件
snap_events = [];
for i = 1:length(start_indices)
    start_idx = max(1, start_indices(i) - 3);
    end_idx = min(length(t), end_indices(i) + 3);
    
    displacement_change = abs(q(end_idx) - q(start_idx));
    max_velocity = max(abs(qd(start_idx:end_idx)));
    duration = t(end_idx) - t(start_idx);
    
    if displacement_change > displacement_threshold && duration < 1.0
        [~, max_vel_idx] = max(abs(qd(start_idx:end_idx)));
        max_vel_idx = start_idx + max_vel_idx - 1;
        
        snap_events = [snap_events; [start_idx, end_idx, t(start_idx), t(end_idx), ...
                      q(start_idx), q(end_idx), T_history(max_vel_idx), ...
                      max_velocity, displacement_change, duration]];
    end
end

fprintf('检测到 %d 个Snap-Through事件\n', size(snap_events,1));

%% 可视化
fprintf('正在生成图表...\n');

figure('Name', 'SMA双稳态梁分析结果', 'Position', [100 100 1200 800]);

% 位移-温度轨迹
subplot(2,2,1);
plot(T_history, q*1000, 'b-', 'LineWidth', 1.5);
hold on;
if ~isempty(snap_events)
    for i = 1:size(snap_events,1)
        event_indices = snap_events(i,1):snap_events(i,2);
        plot(T_history(event_indices), q(event_indices)*1000, 'r-', 'LineWidth', 3);
        plot(T_history(snap_events(i,1)), q(snap_events(i,1))*1000, 'ro', 'MarkerSize', 8);
    end
end
xlabel('温度 [°C]');
ylabel('位移 [mm]');
title('位移-温度轨迹');
grid on;

% 时间历程
subplot(2,2,2);
yyaxis left;
plot(t, q*1000, 'b-', 'LineWidth', 1.5);
ylabel('位移 [mm]');
yyaxis right;
plot(t, T_history, 'r-', 'LineWidth', 1.5);
ylabel('温度 [°C]');
xlabel('时间 [s]');
title('时间历程');
grid on;

% 速度历程
subplot(2,2,3);
plot(t, qd, 'g-', 'LineWidth', 1.5);
hold on;
plot([0 t_total], [velocity_threshold velocity_threshold], 'r--');
plot([0 t_total], [-velocity_threshold -velocity_threshold], 'r--');
xlabel('时间 [s]');
ylabel('速度 [m/s]');
title('速度历程');
grid on;

% 相空间
subplot(2,2,4);
plot(q*1000, qd, 'b-', 'LineWidth', 1);
hold on;
if ~isempty(snap_events)
    for i = 1:size(snap_events,1)
        event_indices = snap_events(i,1):snap_events(i,2);
        plot(q(event_indices)*1000, qd(event_indices), 'r-', 'LineWidth', 3);
    end
end
xlabel('位移 [mm]');
ylabel('速度 [m/s]');
title('相空间轨迹');
grid on;

% 保存结果
if ~exist('Results', 'dir'), mkdir('Results'); end
saveas(gcf, fullfile('Results', 'SMA_Final_Analysis.png'));

%% 输出结果
fprintf('\n=== 最终分析结果 ===\n');
fprintf('计算性能: 用时%.2f秒, %d时间步\n', solve_time, n_steps);
fprintf('位移范围: %.2f - %.2f mm\n', min(q)*1000, max(q)*1000);
fprintf('最大速度: %.3f m/s\n', max(abs(qd)));

if ~isempty(snap_events)
    fprintf('\nSnap-Through事件:\n');
    for i = 1:size(snap_events,1)
        fprintf('事件%d: t=%.2fs, 位移变化=%.2fmm, 速度=%.3fm/s, 温度=%.1f°C\n', ...
                i, snap_events(i,3), snap_events(i,9)*1000, snap_events(i,8), snap_events(i,7));
    end
else
    fprintf('\n未检测到snap-through事件\n');
    fprintf('建议: 增大温度范围或调整系统参数\n');
end

fprintf('\n分析完成！\n');

%% 辅助函数
function k = calculate_SMA_stiffness_hysteresis(T_current, T_previous, M_s, M_f, A_s, A_f, k_M, k_A)
    if T_current >= T_previous
        xi_A = calculate_austenite_fraction(T_current, A_s, A_f);
        k = k_M + (k_A - k_M) * xi_A;
    else
        xi_M = calculate_martensite_fraction(T_current, M_s, M_f);
        k = k_A - (k_A - k_M) * xi_M;
    end
    k = max(min(k_M, k_A), min(max(k_M, k_A), k));
end

function xi_A = calculate_austenite_fraction(T, A_s, A_f)
    if A_f <= A_s, xi_A = (T >= A_s); return; end
    T_mid = (A_s + A_f) / 2;
    T_width = (A_f - A_s) / 4;
    xi_A = 0.5 * (1 + tanh((T - T_mid) / T_width));
    xi_A = max(0, min(1, xi_A));
end

function xi_M = calculate_martensite_fraction(T, M_s, M_f)
    if M_s <= M_f, xi_M = (T <= M_s); return; end
    T_mid = (M_s + M_f) / 2;
    T_width = (M_s - M_f) / 4;
    xi_M = 0.5 * (1 + tanh((T_mid - T) / T_width));
    xi_M = max(0, min(1, xi_M));
end
