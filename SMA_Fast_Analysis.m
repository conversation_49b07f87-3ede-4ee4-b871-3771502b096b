%% SMA双稳态梁快速分析 - 专注于位移vs温度关系
% 功能：快速获得beam中心点位移随温度变化的滞回关系
% 优化：向量化计算、简化模型、快速求解
% 输出：位移-温度滞回曲线和动态snap-through分析
% 作者：热忆阻器系统分析

clear all; close all; clc;

%% 系统参数
fprintf('=== SMA双稳态梁快速分析 ===\n');

% 双稳态梁参数
a = 1000.0;         % 势能系数 [N/m³]
b = 10.0;           % 势能系数 [N/m]
m_eff = 0.01;       % 等效质量 [kg]
c = 0.05;           % 阻尼系数 [N·s/m]

% SMA弹簧参数
H = 0.1;            % 弹簧安装高度 [m]
L_0 = 0.08;         % 弹簧初始长度 [m]
k_M = 50.0;         % 马氏体相刚度 [N/m]
k_A = 500.0;        % 奥氏体相刚度 [N/m]

% 相变温度 [°C]
M_s = 50;  M_f = 40;
A_s = 60;  A_f = 70;

fprintf('系统参数: a=%.0f N/m³, b=%.0f N/m, 稳定点=±%.1fmm\n', a, b, sqrt(b/a)*1000);
fprintf('SMA刚度: %.0f-%.0f N/m, 相变温度: %.0f-%.0f°C\n', k_M, k_A, M_f, A_f);

%% 第一部分：静态位移-温度滞回分析
fprintf('\n=== 静态滞回分析 ===\n');

T_range = linspace(20, 100, 300);
q_heating = zeros(size(T_range));
q_cooling = zeros(size(T_range));

% 升温路径
q_prev = -sqrt(b/a);
tic;
for i = 1:length(T_range)
    T = T_range(i);
    % 简化的SMA刚度计算
    if T <= A_s
        k = k_M;
    elseif T >= A_f
        k = k_A;
    else
        k = k_M + (k_A - k_M) * (T - A_s) / (A_f - A_s);
    end
    
    % 平衡方程求解: a*q³ - (b + k)*q + k*(H - L_0) = 0
    coeffs = [a, 0, -(b + k), k*(H - L_0)];
    q_heating(i) = solve_cubic_newton(coeffs, q_prev);
    q_prev = q_heating(i);
end

% 降温路径
T_cool = flip(T_range);
q_prev = q_heating(end);
for i = 1:length(T_cool)
    T = T_cool(i);
    if T >= M_s
        k = k_A;
    elseif T <= M_f
        k = k_M;
    else
        k = k_A - (k_A - k_M) * (M_s - T) / (M_s - M_f);
    end
    
    coeffs = [a, 0, -(b + k), k*(H - L_0)];
    q_cooling(i) = solve_cubic_newton(coeffs, q_prev);
    q_prev = q_cooling(i);
end
q_cooling = flip(q_cooling);
T_cool = flip(T_cool);

static_time = toc;
fprintf('静态分析完成，用时: %.3f秒\n', static_time);

%% 第二部分：动态snap-through分析
fprintf('\n=== 动态分析 ===\n');

% 简化的动态参数
t_total = 30;       % 总时间 [s]
T_period = 15;      % 温度周期 [s]
T_base = 55;        % 基础温度 [°C]
T_amp = 25;         % 温度振幅 [°C]

% 温度函数
temp_func = @(t) T_base + T_amp * sin(2*pi*t/T_period);

% 初始条件
y0 = [-sqrt(b/a); 0];  % [位移, 速度]

% ODE求解选项（平衡速度和精度）
options = odeset('RelTol', 1e-4, 'AbsTol', 1e-6, 'MaxStep', 0.1);

% 求解动态方程
tic;
[t, y] = ode45(@(t,y) dynamic_ode(t, y, m_eff, c, a, b, H, L_0, ...
               k_M, k_A, M_s, M_f, A_s, A_f, temp_func), [0, t_total], y0, options);
dynamic_time = toc;

q_dynamic = y(:,1);
qd_dynamic = y(:,2);
T_dynamic = arrayfun(temp_func, t);

fprintf('动态分析完成，用时: %.3f秒，时间步数: %d\n', dynamic_time, length(t));

%% 检测snap-through事件
velocity_threshold = 0.05;
snap_indices = find(abs(qd_dynamic) > velocity_threshold);
fprintf('检测到高速度点: %d个\n', length(snap_indices));

%% 可视化结果
fprintf('\n正在生成图表...\n');

figure('Name', 'SMA双稳态梁分析结果', 'Position', [100 100 1400 800]);

% 子图1: 静态滞回环
subplot(2,3,1);
plot(T_range, q_heating*1000, 'r-', 'LineWidth', 2, 'DisplayName', '升温');
hold on;
plot(T_cool, q_cooling*1000, 'b-', 'LineWidth', 2, 'DisplayName', '降温');
xlabel('温度 [°C]');
ylabel('位移 [mm]');
title('静态位移-温度滞回环');
legend('Location', 'best');
grid on;

% 子图2: 动态轨迹
subplot(2,3,2);
plot(T_dynamic, q_dynamic*1000, 'k-', 'LineWidth', 1.5);
hold on;
if ~isempty(snap_indices)
    plot(T_dynamic(snap_indices), q_dynamic(snap_indices)*1000, 'ro', ...
         'MarkerSize', 4, 'MarkerFaceColor', 'r');
end
xlabel('温度 [°C]');
ylabel('位移 [mm]');
title('动态位移-温度轨迹');
grid on;

% 子图3: 时间历程
subplot(2,3,3);
yyaxis left;
plot(t, q_dynamic*1000, 'b-', 'LineWidth', 1.5);
ylabel('位移 [mm]');
yyaxis right;
plot(t, T_dynamic, 'r-', 'LineWidth', 1.5);
ylabel('温度 [°C]');
xlabel('时间 [s]');
title('时间历程');
grid on;

% 子图4: 相空间
subplot(2,3,4);
plot(q_dynamic*1000, qd_dynamic, 'b-', 'LineWidth', 1);
xlabel('位移 [mm]');
ylabel('速度 [m/s]');
title('相空间轨迹');
grid on;

% 子图5: 速度历程
subplot(2,3,5);
plot(t, qd_dynamic, 'g-', 'LineWidth', 1.5);
hold on;
plot([0 t_total], [velocity_threshold velocity_threshold], 'r--', 'LineWidth', 1);
plot([0 t_total], [-velocity_threshold -velocity_threshold], 'r--', 'LineWidth', 1);
xlabel('时间 [s]');
ylabel('速度 [m/s]');
title('速度历程');
grid on;

% 子图6: 对比分析
subplot(2,3,6);
plot(T_range, q_heating*1000, 'r--', 'LineWidth', 1.5, 'DisplayName', '静态升温');
hold on;
plot(T_cool, q_cooling*1000, 'b--', 'LineWidth', 1.5, 'DisplayName', '静态降温');
plot(T_dynamic, q_dynamic*1000, 'k-', 'LineWidth', 1, 'DisplayName', '动态轨迹');
xlabel('温度 [°C]');
ylabel('位移 [mm]');
title('静态vs动态对比');
legend('Location', 'best');
grid on;

% 保存结果
if ~exist('Results', 'dir')
    mkdir('Results');
end
saveas(gcf, fullfile('Results', 'SMA_Fast_Analysis.png'));

%% 输出分析结果
fprintf('\n=== 分析结果总结 ===\n');
fprintf('静态分析:\n');
fprintf('  位移范围: %.2f - %.2f mm\n', min([q_heating; q_cooling])*1000, max([q_heating; q_cooling])*1000);
fprintf('  最大滞回宽度: %.2f mm\n', max(abs(q_heating - q_cooling))*1000);
fprintf('  计算用时: %.3f秒\n', static_time);

fprintf('动态分析:\n');
fprintf('  位移范围: %.2f - %.2f mm\n', min(q_dynamic)*1000, max(q_dynamic)*1000);
fprintf('  最大速度: %.3f m/s\n', max(abs(qd_dynamic)));
fprintf('  高速度事件: %d个\n', length(snap_indices));
fprintf('  计算用时: %.3f秒\n', dynamic_time);

fprintf('\n总用时: %.3f秒\n', static_time + dynamic_time);
fprintf('分析完成！结果已保存到Results文件夹\n');

%% 辅助函数
function q = solve_cubic_newton(coeffs, q_guess)
    % 牛顿法求解三次方程
    a = coeffs(1); c = coeffs(3); d = coeffs(4);
    q = q_guess;
    
    for iter = 1:8  % 限制迭代次数
        f = a*q^3 + c*q + d;
        df = 3*a*q^2 + c;
        
        if abs(df) < 1e-12 || abs(f) < 1e-12
            break;
        end
        
        q_new = q - f/df;
        if abs(q_new - q) < 1e-10
            break;
        end
        q = q_new;
    end
    
    % 检查合理性
    if isnan(q) || isinf(q) || abs(q) > 0.5
        q = q_guess;
    end
end

function dydt = dynamic_ode(t, y, m_eff, c, a, b, H, L_0, k_M, k_A, M_s, M_f, A_s, A_f, temp_func)
    % 动态系统ODE
    persistent T_last
    
    if isempty(T_last)
        T_last = temp_func(0) - 1;
    end
    
    T_current = temp_func(t);
    
    % 简化的滞回刚度计算
    if T_current >= T_last  % 升温
        if T_current <= A_s
            k = k_M;
        elseif T_current >= A_f
            k = k_A;
        else
            k = k_M + (k_A - k_M) * (T_current - A_s) / (A_f - A_s);
        end
    else  % 降温
        if T_current >= M_s
            k = k_A;
        elseif T_current <= M_f
            k = k_M;
        else
            k = k_A - (k_A - k_M) * (M_s - T_current) / (M_s - M_f);
        end
    end
    
    q = y(1);
    qd = y(2);
    
    % 力计算
    F_bistable = -a*q^3 + b*q;
    F_SMA = -k*(q - (H - L_0));
    F_damping = -c*qd;
    
    qdd = (F_bistable + F_SMA + F_damping) / m_eff;
    
    dydt = [qd; qdd];
    T_last = T_current;
end
