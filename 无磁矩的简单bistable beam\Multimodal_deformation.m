clc
clear all
close all

% ---This file demonstrates the force-displacement behavior of a simple 
%    bistable curved beam under mechanical loading (no magnetic field).
%    
%    1. Force-displacement curve calculation using displacement control;
%    2. Analysis of snap-through behavior;
%    3. Calculation of S-ratio for different deformation states;
%    4. Bistable behavior demonstration.
%
% ---The displacement step size is set for smooth curve generation.
%
% ---Mathematical approach: Displacement-controlled analysis where a1 is 
%    the control parameter, a2 is solved from equilibrium, and F is calculated.

%% Initialize Parameters

% Input Parameter 
L = 15*1e-3;        % span of the beam [m]
h = 5*1e-3;         % amplitude of the beam (apex height) [m]
b = 3.5*1e-3;       % width of the beam [m]
t = 0.6*1e-3;       % thickness of the beam [m]
II = b*t^3/12;      % area moment of inertia of the beam 
EE = 3*1e6;         % modulus of beam material [Pa]

% Control parameters
n_steps = 200;      % number of displacement steps
a1_max = h/2;       % maximum a1 value (initial state)
a1_min = -h/2;      % minimum a1 value (snapped state)

% Stationary condition with respect to a1 and a2 (from Symbol_operation.m)
% Note: These are the complex expressions obtained from symbolic computation
syms a1 a2
pp = pi;  

% Internal energy derivatives (obtained from symbolic computation)
% dU_internal/da1 - this gives the generalized force
dU_da1_expr = (EE*II*(16*a1*pp^4 - (327184*a2*pp^2)/10449 - 8*h*pp^4 + (1144*a2*pp^2*cos((43*pp)/50))/43 + (1144*a2*pp^2*cos((243*pp)/50))/243 + 4*a1*pp^3*sin(4*pp) + (40898*a2*pp^3*sin((43*pp)/50))/1075 + (40898*a2*pp^3*sin((243*pp)/50))/6075 - 2*h*pp^3*sin(4*pp)))/(2*L^3) - (2*EE*b*t*(((81796*a2)/10449 - 4*a2*cos(2*pp) - (200*a2*cos((43*pp)/50))/43 + (200*a2*cos((243*pp)/50))/243)/(2*L) - (2*a1*pp^2)/L + (pp*(a1*sin(4*pp) - (286*a2*sin((43*pp)/50))/43 + (286*a2*sin((243*pp)/50))/243))/(2*L))*((4*a2^2*cos((143*pp)/50) - a2^2*cos((143*pp)/25) - (81796*a1*a2)/10449 + 3*a2^2 + 4*a1*a2*cos(2*pp) + (200*a1*a2*cos((43*pp)/50))/43 - (200*a1*a2*cos((243*pp)/50))/243)/(2*L) + ((50*a2^2*sin((143*pp)/25))/143 - (400*a2^2*sin((143*pp)/50))/143)/(2*L*pp) - (pp*((a1^2*sin(4*pp))/2 + (143*a2^2*sin((143*pp)/25))/200 - (286*a1*a2*sin((43*pp)/50))/43 + (286*a1*a2*sin((243*pp)/50))/243))/(2*L) + (pp^2*(2*a1^2 + (20449*a2^2)/5000))/(2*L) - (h^2*pp*(4*pp - sin(4*pp)))/(16*L)))/((pp*(4*pp - sin(4*pp))*h^2)/(16*L) + L);

% dU_internal/da2 = 0 for equilibrium
dU_da2_expr = (EE*II*((20449*a2*pp^2)/1250 - (327184*a1*pp^2)/10449 + (418161601*a2*pp^4)/6250000 + (163592*h*pp^2)/10449 - (143*a2*pp*sin((143*pp)/25))/25 + (1144*a1*pp^2*cos((43*pp)/50))/43 + (20449*a2*pp^2*cos((143*pp)/25))/1250 + (1144*a1*pp^2*cos((243*pp)/50))/243 - (572*h*pp^2*cos((43*pp)/50))/43 - (572*h*pp^2*cos((243*pp)/50))/243 + (40898*a1*pp^3*sin((43*pp)/50))/1075 + (2924207*a2*pp^3*sin((143*pp)/25))/250000 + (40898*a1*pp^3*sin((243*pp)/50))/6075 - (20449*h*pp^3*sin((43*pp)/50))/1075 - (20449*h*pp^3*sin((243*pp)/50))/6075))/(2*L^3) + (2*EE*b*t*((6*a2 - (81796*a1)/10449 + 4*a1*cos(2*pp) + (200*a1*cos((43*pp)/50))/43 - 2*a2*cos((143*pp)/25) + 8*a2*cos((143*pp)/50) - (200*a1*cos((243*pp)/50))/243)/(2*L) + (20449*a2*pp^2)/(5000*L) + ((100*a2*sin((143*pp)/25))/143 - (800*a2*sin((143*pp)/50))/143)/(2*L*pp) - (pp*((143*a2*sin((143*pp)/25))/100 - (286*a1*sin((43*pp)/50))/43 + (286*a1*sin((243*pp)/50))/243))/(2*L))*((4*a2^2*cos((143*pp)/50) - a2^2*cos((143*pp)/25) - (81796*a1*a2)/10449 + 3*a2^2 + 4*a1*a2*cos(2*pp) + (200*a1*a2*cos((43*pp)/50))/43 - (200*a1*a2*cos((243*pp)/50))/243)/(2*L) + ((50*a2^2*sin((143*pp)/25))/143 - (400*a2^2*sin((143*pp)/50))/143)/(2*L*pp) - (pp*((a1^2*sin(4*pp))/2 + (143*a2^2*sin((143*pp)/25))/200 - (286*a1*a2*sin((43*pp)/50))/43 + (286*a1*a2*sin((243*pp)/50))/243))/(2*L) + (pp^2*(2*a1^2 + (20449*a2^2)/5000))/(2*L) - (h^2*pp*(4*pp - sin(4*pp)))/(16*L)))/((pp*(4*pp - sin(4*pp))*h^2)/(16*L) + L);

%% Forward Loading Path (C1 to C2 - compression)
fprintf('Computing forward loading path (C1 to C2)...\n');

% Displacement range for forward path
a1_range_forward = linspace(a1_max, a1_min, n_steps);
a2_sol_forward = zeros(size(a1_range_forward));
force_forward = zeros(size(a1_range_forward));
displacement_forward = zeros(size(a1_range_forward));

% Initial guess for a2
a2_guess = 0;

for k = 1:length(a1_range_forward)
    current_a1 = a1_range_forward(k);
    
    % 1. Solve for a2 from equilibrium condition: dU_da2 = 0
    % Convert symbolic expression to function handle
    dU_da2_func = @(a2_var) double(subs(dU_da2_expr, [a1, a2], [current_a1, a2_var]));
    
    % Solve for a2
    options = optimoptions('fsolve', 'Display', 'none', 'TolFun', 1e-12, 'TolX', 1e-12);
    [a2_val, ~, exitflag] = fsolve(dU_da2_func, a2_guess, options);
    
    if exitflag <= 0
        % Try different initial guess if convergence fails
        [a2_val, ~, exitflag] = fsolve(dU_da2_func, 0, options);
        if exitflag <= 0
            warning('a2 solver failed at forward step %d', k);
            % Use previous value or interpolation
            if k > 1
                a2_val = a2_sol_forward(k-1);
            else
                a2_val = 0;
            end
        end
    end
    
    a2_sol_forward(k) = a2_val;
    a2_guess = a2_val; % Update guess for next iteration
    
    % 2. Calculate force: F = (1/2) * dU_da1 (simplified relationship)
    dU_da1_val = double(subs(dU_da1_expr, [a1, a2], [current_a1, a2_sol_forward(k)]));
    force_forward(k) = dU_da1_val / 2; % Simplified force calculation
    
    % 3. Calculate center displacement: delta ≈ 2*a1 (simplified)
    displacement_forward(k) = 2 * current_a1;
    
    % Progress indication
    if mod(k, 50) == 0
        fprintf('Forward step %d/%d completed\n', k, length(a1_range_forward));
    end
end

%% Reverse Loading Path (C2 to C1 - tension/release)
fprintf('Computing reverse loading path (C2 to C1)...\n');

% Displacement range for reverse path (starting from snapped state)
a1_range_reverse = linspace(a1_min, a1_max, n_steps);
a2_sol_reverse = zeros(size(a1_range_reverse));
force_reverse = zeros(size(a1_range_reverse));
displacement_reverse = zeros(size(a1_range_reverse));

% Initial guess for a2 (start from last forward solution)
a2_guess = a2_sol_forward(end);

for k = 1:length(a1_range_reverse)
    current_a1 = a1_range_reverse(k);
    
    % 1. Solve for a2 from equilibrium condition: dU_da2 = 0
    dU_da2_func = @(a2_var) double(subs(dU_da2_expr, [a1, a2], [current_a1, a2_var]));
    
    % Solve for a2
    options = optimoptions('fsolve', 'Display', 'none', 'TolFun', 1e-12, 'TolX', 1e-12);
    [a2_val, ~, exitflag] = fsolve(dU_da2_func, a2_guess, options);
    
    if exitflag <= 0
        % Try different initial guess if convergence fails
        [a2_val, ~, exitflag] = fsolve(dU_da2_func, 0, options);
        if exitflag <= 0
            warning('a2 solver failed at reverse step %d', k);
            if k > 1
                a2_val = a2_sol_reverse(k-1);
            else
                a2_val = a2_sol_forward(end);
            end
        end
    end
    
    a2_sol_reverse(k) = a2_val;
    a2_guess = a2_val;
    
    % 2. Calculate force
    dU_da1_val = double(subs(dU_da1_expr, [a1, a2], [current_a1, a2_sol_reverse(k)]));
    force_reverse(k) = dU_da1_val / 2;
    
    % 3. Calculate center displacement
    displacement_reverse(k) = 2 * current_a1;
    
    % Progress indication
    if mod(k, 50) == 0
        fprintf('Reverse step %d/%d completed\n', k, length(a1_range_reverse));
    end
end

%% Plot Force-Displacement Curve
fontsize = 14;
figure(1);
clf;
plot(displacement_forward * 1000, force_forward, 'b-', 'LineWidth', 2, 'DisplayName', 'Loading (C1→C2)');
hold on;
plot(displacement_reverse * 1000, force_reverse, 'r--', 'LineWidth', 2, 'DisplayName', 'Unloading (C2→C1)');
grid on;
xlabel('Center Displacement (mm)', 'FontSize', fontsize, 'FontWeight', 'bold');
ylabel('Applied Force (N)', 'FontSize', fontsize, 'FontWeight', 'bold');
title('Force-Displacement Curve of Bistable Beam', 'FontSize', fontsize, 'FontWeight', 'bold');
legend('Location', 'best');
set(gca, 'FontSize', fontsize);

% Add annotations for snap points
[~, snap_forward_idx] = max(force_forward);
[~, snap_reverse_idx] = min(force_reverse);
plot(displacement_forward(snap_forward_idx) * 1000, force_forward(snap_forward_idx), 'ko', 'MarkerSize', 8, 'MarkerFaceColor', 'blue');
plot(displacement_reverse(snap_reverse_idx) * 1000, force_reverse(snap_reverse_idx), 'ko', 'MarkerSize', 8, 'MarkerFaceColor', 'red');

fprintf('\nSnap-through analysis:\n');
fprintf('Forward snap force: %.4f N at displacement: %.3f mm\n', force_forward(snap_forward_idx), displacement_forward(snap_forward_idx)*1000);
fprintf('Reverse snap force: %.4f N at displacement: %.3f mm\n', force_reverse(snap_reverse_idx), displacement_reverse(snap_reverse_idx)*1000);

%% Plot Deformed Shapes at Key Points
figure(2);
clf;
xplot = linspace(0, L, 101);

% Initial shape
y_initial = h/2 * (1 - cos(2*pi*xplot/L));
plot(xplot*1000, y_initial*1000, 'k--', 'LineWidth', 1.5, 'DisplayName', 'Initial C1 State');
hold on;

% Select key deformation states
key_indices = [1, snap_forward_idx, length(a1_range_forward), snap_reverse_idx];
key_labels = {'Initial C1', 'Forward Snap', 'Final C2', 'Reverse Snap'};
colors = {'b', 'g', 'r', 'm'};

for i = 1:length(key_indices)
    idx = key_indices(i);
    if idx <= length(a1_range_forward)
        a1_key = a1_range_forward(idx);
        a2_key = a2_sol_forward(idx);
    else
        idx_rev = idx - length(a1_range_forward);
        a1_key = a1_range_reverse(idx_rev);
        a2_key = a2_sol_reverse(idx_rev);
    end
    
    % Calculate deformed shape
    y_deformed = a1_key * (1 - cos(2*pi*xplot/L)) + ...
                 a2_key * (1 - 2*xplot/L - cos(2.86*pi*xplot/L) + 2/(2.86*pi)*sin(2.86*pi*xplot/L));
    
    plot(xplot*1000, y_deformed*1000, 'Color', colors{i}, 'LineWidth', 2, 'DisplayName', key_labels{i});
end

xlabel('Position x (mm)', 'FontSize', fontsize, 'FontWeight', 'bold');
ylabel('Deflection y (mm)', 'FontSize', fontsize, 'FontWeight', 'bold');
title('Beam Deformation at Key Loading States', 'FontSize', fontsize, 'FontWeight', 'bold');
legend('Location', 'best');
grid on;
axis equal;
set(gca, 'FontSize', fontsize);

%% Calculate and Plot S-ratio
figure(3);
clf;
S_ratio_forward = zeros(size(a1_range_forward));
S_ratio_reverse = zeros(size(a1_range_reverse));

for k = 1:length(a1_range_forward)
    y_shape = a1_range_forward(k) * (1 - cos(2*pi*xplot/L)) + ...
              a2_sol_forward(k) * (1 - 2*xplot/L - cos(2.86*pi*xplot/L) + 2/(2.86*pi)*sin(2.86*pi*xplot/L));
    y_max = max(abs(y_shape));
    y_min = min(abs(y_shape));
    if y_max > 0
        S_ratio_forward(k) = y_min / y_max;
    else
        S_ratio_forward(k) = 0;
    end
end

for k = 1:length(a1_range_reverse)
    y_shape = a1_range_reverse(k) * (1 - cos(2*pi*xplot/L)) + ...
              a2_sol_reverse(k) * (1 - 2*xplot/L - cos(2.86*pi*xplot/L) + 2/(2.86*pi)*sin(2.86*pi*xplot/L));
    y_max = max(abs(y_shape));
    y_min = min(abs(y_shape));
    if y_max > 0
        S_ratio_reverse(k) = y_min / y_max;
    else
        S_ratio_reverse(k) = 0;
    end
end

plot(displacement_forward * 1000, S_ratio_forward, 'b-', 'LineWidth', 2, 'DisplayName', 'Loading');
hold on;
plot(displacement_reverse * 1000, S_ratio_reverse, 'r--', 'LineWidth', 2, 'DisplayName', 'Unloading');
xlabel('Center Displacement (mm)', 'FontSize', fontsize, 'FontWeight', 'bold');
ylabel('S-ratio', 'FontSize', fontsize, 'FontWeight', 'bold');
title('S-ratio vs Displacement', 'FontSize', fontsize, 'FontWeight', 'bold');
legend('Location', 'best');
grid on;
set(gca, 'FontSize', fontsize);

% Add critical S-ratio line
S_crit = 0.3;
yline(S_crit, 'k--', 'LineWidth', 1, 'DisplayName', 'Critical S-ratio = 0.3');

%% Summary Output
fprintf('\n=== Bistable Beam Analysis Summary ===\n');
fprintf('Beam parameters:\n');
fprintf('  Length: %.1f mm\n', L*1000);
fprintf('  Height: %.1f mm\n', h*1000);
fprintf('  Width: %.1f mm\n', b*1000);
fprintf('  Thickness: %.1f mm\n', t*1000);
fprintf('  Young''s modulus: %.1f MPa\n', EE/1e6);
fprintf('\nForce-displacement characteristics:\n');
fprintf('  Maximum forward force: %.4f N\n', max(force_forward));
fprintf('  Minimum reverse force: %.4f N\n', min(force_reverse));
fprintf('  Total displacement range: %.3f mm\n', (max(displacement_forward) - min(displacement_forward))*1000);
fprintf('  Hysteresis area: %.6f N·mm\n', trapz([displacement_forward, displacement_reverse]*1000, [force_forward, force_reverse]));

fprintf('\nAnalysis completed successfully!\n');