%% SMA双稳态梁增强分析 - 保持tanh滞回模型 + 改进snap-through检测
% 功能：快速分析 + 精确的滞回模型 + 智能snap-through检测
% 特点：保持原有tanh相变模型，增强事件检测能力

clear all; close all; clc;

%% 系统参数
fprintf('=== SMA双稳态梁增强分析 ===\n');

% 双稳态梁参数
a = 1000.0;         % 势能系数 [N/m³]
b = 10.0;           % 势能系数 [N/m]
m_eff = 0.01;       % 等效质量 [kg]
c = 0.05;           % 阻尼系数 [N·s/m]

% SMA弹簧参数
H = 0.1;            % 弹簧安装高度 [m]
L_0 = 0.08;         % 弹簧初始长度 [m]
k_M = 50.0;         % 马氏体相刚度 [N/m]
k_A = 500.0;        % 奥氏体相刚度 [N/m]

% 相变温度 [°C]
M_s = 50;  M_f = 40;
A_s = 60;  A_f = 70;

% 动态仿真参数
t_total = 30;       % 总时间 [s]
T_period = 15;      % 温度周期 [s]
T_base = 55;        % 基础温度 [°C]
T_amp = 25;         % 温度振幅 [°C]

fprintf('系统参数: a=%.0f N/m³, b=%.0f N/m, 稳定点=±%.1fmm\n', a, b, sqrt(b/a)*1000);
fprintf('SMA刚度: %.0f-%.0f N/m, 相变温度: %.0f-%.0f°C\n', k_M, k_A, M_f, A_f);
fprintf('动态参数: 总时间%.0fs, 温度周期%.0fs\n', t_total, T_period);

%% 第一部分：静态滞回分析（使用tanh模型）
fprintf('\n=== 静态滞回分析（tanh模型）===\n');

T_range = linspace(20, 100, 300);
q_heating = zeros(size(T_range));
q_cooling = zeros(size(T_range));

tic;
% 升温路径
q_prev = -sqrt(b/a);
T_prev = T_range(1) - 5;
for i = 1:length(T_range)
    T = T_range(i);
    k = calculate_SMA_stiffness_hysteresis(T, T_prev, M_s, M_f, A_s, A_f, k_M, k_A);
    coeffs = [a, 0, -(b + k), k*(H - L_0)];
    q_heating(i) = solve_cubic_newton(coeffs, q_prev);
    q_prev = q_heating(i);
    T_prev = T;
end

% 降温路径
T_cool = flip(T_range);
q_prev = q_heating(end);
T_prev = T_cool(1) + 5;
for i = 1:length(T_cool)
    T = T_cool(i);
    k = calculate_SMA_stiffness_hysteresis(T, T_prev, M_s, M_f, A_s, A_f, k_M, k_A);
    coeffs = [a, 0, -(b + k), k*(H - L_0)];
    q_cooling(i) = solve_cubic_newton(coeffs, q_prev);
    q_prev = q_cooling(i);
    T_prev = T;
end
q_cooling = flip(q_cooling);
T_cool = flip(T_cool);

static_time = toc;
fprintf('静态分析完成，用时: %.3f秒\n', static_time);

%% 第二部分：动态分析
fprintf('\n=== 动态分析 ===\n');

% 温度函数
temp_func = @(t) T_base + T_amp * sin(2*pi*t/T_period);

% 初始条件
y0 = [-sqrt(b/a); 0];

% 优化的ODE求解选项
options = odeset('RelTol', 1e-5, 'AbsTol', 1e-7, 'MaxStep', 0.05);

% 求解动态方程
tic;
[t, y] = ode45(@(t,y) dynamic_ode_tanh(t, y, m_eff, c, a, b, H, L_0, ...
               M_s, M_f, A_s, A_f, k_M, k_A, temp_func), [0, t_total], y0, options);
dynamic_time = toc;

q_dynamic = y(:,1);
qd_dynamic = y(:,2);
T_dynamic = arrayfun(temp_func, t);

fprintf('动态分析完成，用时: %.3f秒，时间步数: %d\n', dynamic_time, length(t));

%% 第三部分：增强的snap-through检测
fprintf('\n=== 增强Snap-Through检测 ===\n');

% 计算加速度
qdd_numeric = gradient(qd_dynamic, t);

% 多重检测标准
velocity_threshold = 0.03;
acceleration_threshold = 3.0;
displacement_threshold = 0.01;

% 检测候选点
high_vel_mask = abs(qd_dynamic) > velocity_threshold;
high_acc_mask = abs(qdd_numeric) > acceleration_threshold;
candidate_mask = high_vel_mask | high_acc_mask;
candidate_indices = find(candidate_mask);

% 智能分组和验证
snap_events = [];
if ~isempty(candidate_indices)
    % 分组连续事件
    groups = [];
    current_group = [candidate_indices(1)];
    
    for i = 2:length(candidate_indices)
        if t(candidate_indices(i)) - t(candidate_indices(i-1)) <= 0.3
            current_group = [current_group, candidate_indices(i)];
        else
            groups{end+1} = current_group;
            current_group = [candidate_indices(i)];
        end
    end
    groups{end+1} = current_group;
    
    % 验证每个组
    for i = 1:length(groups)
        group = groups{i};
        start_idx = max(1, group(1) - 3);
        end_idx = min(length(t), group(end) + 3);
        
        displacement_change = abs(q_dynamic(end_idx) - q_dynamic(start_idx));
        max_velocity = max(abs(qd_dynamic(start_idx:end_idx)));
        
        if displacement_change > displacement_threshold || max_velocity > velocity_threshold * 1.5
            [~, max_vel_idx] = max(abs(qd_dynamic(start_idx:end_idx)));
            max_vel_idx = start_idx + max_vel_idx - 1;
            
            snap_events = [snap_events; [start_idx, end_idx, t(start_idx), t(end_idx), ...
                          q_dynamic(start_idx), q_dynamic(end_idx), T_dynamic(max_vel_idx), ...
                          max_velocity, displacement_change]];
        end
    end
end

fprintf('检测到 %d 个Snap-Through事件\n', size(snap_events,1));

%% 可视化结果
fprintf('\n正在生成分析图表...\n');

figure('Name', 'SMA双稳态梁增强分析', 'Position', [100 100 1400 1000]);

% 子图1: 静态滞回环
subplot(2,3,1);
plot(T_range, q_heating*1000, 'r-', 'LineWidth', 2, 'DisplayName', '升温（tanh）');
hold on;
plot(T_cool, q_cooling*1000, 'b-', 'LineWidth', 2, 'DisplayName', '降温（tanh）');
xlabel('温度 [°C]');
ylabel('位移 [mm]');
title('静态滞回环（tanh模型）');
legend('Location', 'best');
grid on;

% 子图2: 动态轨迹
subplot(2,3,2);
plot(T_dynamic, q_dynamic*1000, 'k-', 'LineWidth', 1.5, 'DisplayName', '动态轨迹');
hold on;
if ~isempty(snap_events)
    for i = 1:size(snap_events,1)
        event_indices = snap_events(i,1):snap_events(i,2);
        plot(T_dynamic(event_indices), q_dynamic(event_indices)*1000, 'r-', 'LineWidth', 3);
        plot(T_dynamic(snap_events(i,1)), q_dynamic(snap_events(i,1))*1000, 'ro', ...
             'MarkerSize', 8, 'MarkerFaceColor', 'r');
    end
end
xlabel('温度 [°C]');
ylabel('位移 [mm]');
title('动态轨迹 + Snap-Through');
grid on;

% 子图3: 时间历程
subplot(2,3,3);
yyaxis left;
plot(t, q_dynamic*1000, 'b-', 'LineWidth', 1.5);
ylabel('位移 [mm]');
yyaxis right;
plot(t, T_dynamic, 'r-', 'LineWidth', 1.5);
ylabel('温度 [°C]');
xlabel('时间 [s]');
title('时间历程');
grid on;

% 子图4: 速度和加速度
subplot(2,3,4);
yyaxis left;
plot(t, qd_dynamic, 'g-', 'LineWidth', 1.5);
ylabel('速度 [m/s]');
yyaxis right;
plot(t, qdd_numeric, 'm-', 'LineWidth', 1);
ylabel('加速度 [m/s²]');
xlabel('时间 [s]');
title('速度和加速度');
grid on;

% 子图5: 相空间
subplot(2,3,5);
plot(q_dynamic*1000, qd_dynamic, 'b-', 'LineWidth', 1);
hold on;
if ~isempty(snap_events)
    for i = 1:size(snap_events,1)
        event_indices = snap_events(i,1):snap_events(i,2);
        plot(q_dynamic(event_indices)*1000, qd_dynamic(event_indices), 'r-', 'LineWidth', 3);
    end
end
xlabel('位移 [mm]');
ylabel('速度 [m/s]');
title('相空间轨迹');
grid on;

% 子图6: 对比分析
subplot(2,3,6);
plot(T_range, q_heating*1000, 'r--', 'LineWidth', 1.5, 'DisplayName', '静态升温');
hold on;
plot(T_cool, q_cooling*1000, 'b--', 'LineWidth', 1.5, 'DisplayName', '静态降温');
plot(T_dynamic, q_dynamic*1000, 'k-', 'LineWidth', 1, 'DisplayName', '动态轨迹');
xlabel('温度 [°C]');
ylabel('位移 [mm]');
title('静态vs动态对比');
legend('Location', 'best');
grid on;

% 保存结果
if ~exist('Results', 'dir')
    mkdir('Results');
end
saveas(gcf, fullfile('Results', 'SMA_Enhanced_Analysis.png'));

%% 输出分析结果
fprintf('\n=== 分析结果总结 ===\n');
fprintf('静态分析（tanh模型）:\n');
fprintf('  位移范围: %.2f - %.2f mm\n', min([q_heating; q_cooling])*1000, max([q_heating; q_cooling])*1000);
fprintf('  最大滞回宽度: %.2f mm\n', max(abs(q_heating - q_cooling))*1000);
fprintf('  计算用时: %.3f秒\n', static_time);

fprintf('动态分析:\n');
fprintf('  位移范围: %.2f - %.2f mm\n', min(q_dynamic)*1000, max(q_dynamic)*1000);
fprintf('  最大速度: %.3f m/s\n', max(abs(qd_dynamic)));
fprintf('  计算用时: %.3f秒\n', dynamic_time);

if ~isempty(snap_events)
    fprintf('\nSnap-Through事件详情:\n');
    fprintf('编号  时间[s]    位移变化[mm]  最大速度[m/s]  温度[°C]\n');
    for i = 1:size(snap_events,1)
        fprintf('%2d    %.2f       %8.2f     %8.3f      %6.1f\n', ...
                i, snap_events(i,3), snap_events(i,9)*1000, snap_events(i,8), snap_events(i,7));
    end
end

fprintf('\n总用时: %.3f秒\n', static_time + dynamic_time);
fprintf('分析完成！\n');

%% 辅助函数
function k = calculate_SMA_stiffness_hysteresis(T_current, T_previous, M_s, M_f, A_s, A_f, k_M, k_A)
    % 原有的tanh滞回模型
    if T_current >= T_previous
        xi_A = calculate_austenite_fraction(T_current, A_s, A_f);
        k = k_M + (k_A - k_M) * xi_A;
    else
        xi_M = calculate_martensite_fraction(T_current, M_s, M_f);
        k = k_A - (k_A - k_M) * xi_M;
    end
    k = max(min(k_M, k_A), min(max(k_M, k_A), k));
end

function xi_A = calculate_austenite_fraction(T, A_s, A_f)
    if A_f <= A_s
        xi_A = (T >= A_s);
        return;
    end
    T_mid = (A_s + A_f) / 2;
    T_width = (A_f - A_s) / 4;
    xi_A = 0.5 * (1 + tanh((T - T_mid) / T_width));
    xi_A = max(0, min(1, xi_A));
end

function xi_M = calculate_martensite_fraction(T, M_s, M_f)
    if M_s <= M_f
        xi_M = (T <= M_s);
        return;
    end
    T_mid = (M_s + M_f) / 2;
    T_width = (M_s - M_f) / 4;
    xi_M = 0.5 * (1 + tanh((T_mid - T) / T_width));
    xi_M = max(0, min(1, xi_M));
end

function q = solve_cubic_newton(coeffs, q_guess)
    a = coeffs(1); c = coeffs(3); d = coeffs(4);
    q = q_guess;
    for iter = 1:6
        f = a*q^3 + c*q + d;
        df = 3*a*q^2 + c;
        if abs(df) < 1e-12 || abs(f) < 1e-12, break; end
        q_new = q - f/df;
        if abs(q_new - q) < 1e-10, break; end
        q = q_new;
    end
    if isnan(q) || isinf(q) || abs(q) > 0.5, q = q_guess; end
end

function dydt = dynamic_ode_tanh(t, y, m_eff, c, a, b, H, L_0, M_s, M_f, A_s, A_f, k_M, k_A, temp_func)
    persistent T_last
    if isempty(T_last), T_last = temp_func(0) - 1; end
    
    T_current = temp_func(t);
    k = calculate_SMA_stiffness_hysteresis(T_current, T_last, M_s, M_f, A_s, A_f, k_M, k_A);
    
    q = y(1); qd = y(2);
    F_bistable = -a*q^3 + b*q;
    F_SMA = -k*(q - (H - L_0));
    F_damping = -c*qd;
    qdd = (F_bistable + F_SMA + F_damping) / m_eff;
    
    dydt = [qd; qdd];
    T_last = T_current;
end
