clc
clear all
close all

% ---This file calculates the critical parameters for the simple bistable beam:
%    1. Critical forces for snap-through behavior;
%    2. Stability analysis and ranges;
%    3. Detailed force-displacement characteristics;
%    4. Energy landscape analysis.
%
% ---This analysis uses finer discretization for accurate critical point detection.
%
% ---Key outputs: critical snap forces, stable displacement ranges, 
%    energy barriers, and stability boundaries.

%% Initialize Parameters

% Input Parameter 
L = 15*1e-3;        % span of the beam [m]
h = 5*1e-3;         % amplitude of the beam (apex height) [m]
b = 3.5*1e-3;       % width of the beam [m]
t = 0.6*1e-3;       % thickness of the beam [m]
II = b*t^3/12;      % area moment of inertia of the beam 
EE = 3*1e6;         % modulus of beam material [Pa]

% High-resolution analysis parameters
n_steps = 500;      % number of displacement steps for fine analysis
a1_max = h/2;       % maximum a1 value (initial state)
a1_min = -h/2;      % minimum a1 value (snapped state)

% Stationary condition expressions (from Symbol_operation.m)
syms a1 a2
pp = pi;  

% Internal energy derivatives
dU_da1_expr = (EE*II*(16*a1*pp^4 - (327184*a2*pp^2)/10449 - 8*h*pp^4 + (1144*a2*pp^2*cos((43*pp)/50))/43 + (1144*a2*pp^2*cos((243*pp)/50))/243 + 4*a1*pp^3*sin(4*pp) + (40898*a2*pp^3*sin((43*pp)/50))/1075 + (40898*a2*pp^3*sin((243*pp)/50))/6075 - 2*h*pp^3*sin(4*pp)))/(2*L^3) - (2*EE*b*t*(((81796*a2)/10449 - 4*a2*cos(2*pp) - (200*a2*cos((43*pp)/50))/43 + (200*a2*cos((243*pp)/50))/243)/(2*L) - (2*a1*pp^2)/L + (pp*(a1*sin(4*pp) - (286*a2*sin((43*pp)/50))/43 + (286*a2*sin((243*pp)/50))/243))/(2*L))*((4*a2^2*cos((143*pp)/50) - a2^2*cos((143*pp)/25) - (81796*a1*a2)/10449 + 3*a2^2 + 4*a1*a2*cos(2*pp) + (200*a1*a2*cos((43*pp)/50))/43 - (200*a1*a2*cos((243*pp)/50))/243)/(2*L) + ((50*a2^2*sin((143*pp)/25))/143 - (400*a2^2*sin((143*pp)/50))/143)/(2*L*pp) - (pp*((a1^2*sin(4*pp))/2 + (143*a2^2*sin((143*pp)/25))/200 - (286*a1*a2*sin((43*pp)/50))/43 + (286*a1*a2*sin((243*pp)/50))/243))/(2*L) + (pp^2*(2*a1^2 + (20449*a2^2)/5000))/(2*L) - (h^2*pp*(4*pp - sin(4*pp)))/(16*L)))/((pp*(4*pp - sin(4*pp))*h^2)/(16*L) + L);

dU_da2_expr = (EE*II*((20449*a2*pp^2)/1250 - (327184*a1*pp^2)/10449 + (418161601*a2*pp^4)/6250000 + (163592*h*pp^2)/10449 - (143*a2*pp*sin((143*pp)/25))/25 + (1144*a1*pp^2*cos((43*pp)/50))/43 + (20449*a2*pp^2*cos((143*pp)/25))/1250 + (1144*a1*pp^2*cos((243*pp)/50))/243 - (572*h*pp^2*cos((43*pp)/50))/43 - (572*h*pp^2*cos((243*pp)/50))/243 + (40898*a1*pp^3*sin((43*pp)/50))/1075 + (2924207*a2*pp^3*sin((143*pp)/25))/250000 + (40898*a1*pp^3*sin((243*pp)/50))/6075 - (20449*h*pp^3*sin((43*pp)/50))/1075 - (20449*h*pp^3*sin((243*pp)/50))/6075))/(2*L^3) + (2*EE*b*t*((6*a2 - (81796*a1)/10449 + 4*a1*cos(2*pp) + (200*a1*cos((43*pp)/50))/43 - 2*a2*cos((143*pp)/25) + 8*a2*cos((143*pp)/50) - (200*a1*cos((243*pp)/50))/243)/(2*L) + (20449*a2*pp^2)/(5000*L) + ((100*a2*sin((143*pp)/25))/143 - (800*a2*sin((143*pp)/50))/143)/(2*L*pp) - (pp*((143*a2*sin((143*pp)/25))/100 - (286*a1*sin((43*pp)/50))/43 + (286*a1*sin((243*pp)/50))/243))/(2*L))*((4*a2^2*cos((143*pp)/50) - a2^2*cos((143*pp)/25) - (81796*a1*a2)/10449 + 3*a2^2 + 4*a1*a2*cos(2*pp) + (200*a1*a2*cos((43*pp)/50))/43 - (200*a1*a2*cos((243*pp)/50))/243)/(2*L) + ((50*a2^2*sin((143*pp)/25))/143 - (400*a2^2*sin((143*pp)/50))/143)/(2*L*pp) - (pp*((a1^2*sin(4*pp))/2 + (143*a2^2*sin((143*pp)/25))/200 - (286*a1*a2*sin((43*pp)/50))/43 + (286*a1*a2*sin((243*pp)/50))/243))/(2*L) + (pp^2*(2*a1^2 + (20449*a2^2)/5000))/(2*L) - (h^2*pp*(4*pp - sin(4*pp)))/(16*L)))/((pp*(4*pp - sin(4*pp))*h^2)/(16*L) + L);

%% High-Resolution Force-Displacement Analysis

fprintf('Computing high-resolution force-displacement curve...\n');

% Fine displacement grid
a1_range = linspace(a1_max, a1_min, n_steps);
a2_sol = zeros(size(a1_range));
force = zeros(size(a1_range));
displacement = zeros(size(a1_range));
stiffness = zeros(size(a1_range));

% Initialize solving
a2_guess = 0;
solve_failed = false(size(a1_range));

for k = 1:length(a1_range)
    current_a1 = a1_range(k);
    
    % Solve for a2 from equilibrium: dU_da2 = 0
    dU_da2_func = @(a2_var) double(subs(dU_da2_expr, [a1, a2], [current_a1, a2_var]));
    
    options = optimoptions('fsolve', 'Display', 'none', 'TolFun', 1e-14, 'TolX', 1e-14, 'MaxIterations', 1000);
    [a2_val, ~, exitflag] = fsolve(dU_da2_func, a2_guess, options);
    
    if exitflag <= 0
        % Try different initial guesses
        init_guesses = [0, a2_guess*0.5, a2_guess*1.5, -a2_guess, 0.001, -0.001];
        success = false;
        for guess = init_guesses
            [a2_val, ~, exitflag] = fsolve(dU_da2_func, guess, options);
            if exitflag > 0
                success = true;
                break;
            end
        end
        if ~success
            solve_failed(k) = true;
            warning('a2 solver failed at step %d, a1 = %.6f', k, current_a1);
            if k > 1
                a2_val = a2_sol(k-1);
            else
                a2_val = 0;
            end
        end
    end
    
    a2_sol(k) = a2_val;
    a2_guess = a2_val;
    
    % Calculate force
    dU_da1_val = double(subs(dU_da1_expr, [a1, a2], [current_a1, a2_sol(k)]));
    force(k) = dU_da1_val / 2; % Simplified force calculation
    
    % Calculate displacement
    displacement(k) = 2 * current_a1;
    
    % Calculate stiffness (numerical derivative of force)
    if k > 1
        stiffness(k) = (force(k) - force(k-1)) / (displacement(k) - displacement(k-1));
    end
    
    % Progress indication
    if mod(k, 100) == 0
        fprintf('Step %d/%d completed\n', k, length(a1_range));
    end
end

% Handle first stiffness point
stiffness(1) = stiffness(2);

%% Critical Point Analysis

fprintf('\nAnalyzing critical points...\n');

% Find maximum and minimum forces (critical points)
[F_max, idx_max] = max(force);
[F_min, idx_min] = min(force);

% Find zero-stiffness points (instability points)
stiffness_smooth = smoothdata(stiffness, 'movmean', 5); % Smooth for noise reduction
zero_crossings = find(diff(sign(stiffness_smooth)) ~= 0);

% Critical displacements and forces
delta_crit_max = displacement(idx_max);
delta_crit_min = displacement(idx_min);
F_crit_forward = F_max;  % Maximum force for forward snap
F_crit_reverse = F_min;  % Minimum force for reverse snap

% Stable ranges analysis
stable_idx = stiffness_smooth > 0;  % Positive stiffness = stable
unstable_idx = stiffness_smooth < 0; % Negative stiffness = unstable

% Find stability boundaries
stability_changes = find(diff(stable_idx) ~= 0);

fprintf('=== Critical Force Analysis ===\n');
fprintf('Forward snap-through:\n');
fprintf('  Critical force: %.6f N\n', F_crit_forward);
fprintf('  Critical displacement: %.4f mm\n', delta_crit_max * 1000);
fprintf('Reverse snap-through:\n');
fprintf('  Critical force: %.6f N\n', F_crit_reverse);
fprintf('  Critical displacement: %.4f mm\n', delta_crit_min * 1000);
fprintf('Force range: %.6f N\n', F_crit_forward - F_crit_reverse);
fprintf('Displacement range: %.4f mm\n', (delta_crit_max - delta_crit_min) * 1000);

%% S-ratio Analysis with Critical Values

S_ratio = zeros(size(a1_range));
xplot = linspace(0, L, 101);

for k = 1:length(a1_range)
    if ~solve_failed(k)
        y_shape = a1_range(k) * (1 - cos(2*pi*xplot/L)) + ...
                  a2_sol(k) * (1 - 2*xplot/L - cos(2.86*pi*xplot/L) + 2/(2.86*pi)*sin(2.86*pi*xplot/L));
        y_max = max(abs(y_shape));
        y_min = min(abs(y_shape));
        if y_max > 0
            S_ratio(k) = y_min / y_max;
        end
    end
end

% Find S-shape range (S-ratio > critical value)
S_crit = 0.3;
S_range_idx = S_ratio >= S_crit;
if any(S_range_idx)
    S_start_disp = displacement(find(S_range_idx, 1, 'first'));
    S_end_disp = displacement(find(S_range_idx, 1, 'last'));
    S_range_length = S_start_disp - S_end_disp;
    
    fprintf('\n=== S-Shape Analysis ===\n');
    fprintf('S-shape range: %.4f mm to %.4f mm\n', S_end_disp * 1000, S_start_disp * 1000);
    fprintf('S-shape range length: %.4f mm\n', S_range_length * 1000);
else
    fprintf('\nNo significant S-shape behavior detected.\n');
end

%% Energy Landscape Analysis

% Calculate internal energy
U_internal = zeros(size(a1_range));
for k = 1:length(a1_range)
    if ~solve_failed(k)
        % Calculate internal energy by integrating force
        if k == 1
            U_internal(k) = 0; % Reference point
        else
            U_internal(k) = U_internal(k-1) + force(k) * (displacement(k) - displacement(k-1));
        end
    end
end

% Energy barriers
[U_max, idx_U_max] = max(U_internal);
[U_min, idx_U_min] = min(U_internal);
energy_barrier_forward = U_max - U_internal(1);
energy_barrier_reverse = U_max - U_internal(end);

fprintf('\n=== Energy Analysis ===\n');
fprintf('Forward energy barrier: %.8f J\n', energy_barrier_forward);
fprintf('Reverse energy barrier: %.8f J\n', energy_barrier_reverse);
fprintf('Total energy change: %.8f J\n', U_internal(end) - U_internal(1));

%% Advanced Plotting

% Figure 1: Force-Displacement with Critical Points
fontsize = 14;
figure(1);
clf;
plot(displacement * 1000, force, 'b-', 'LineWidth', 2);
hold on;
plot(delta_crit_max * 1000, F_crit_forward, 'ro', 'MarkerSize', 10, 'MarkerFaceColor', 'red', 'DisplayName', 'Forward Snap');
plot(delta_crit_min * 1000, F_crit_reverse, 'go', 'MarkerSize', 10, 'MarkerFaceColor', 'green', 'DisplayName', 'Reverse Snap');

% Mark stability regions
stable_mask = stable_idx;
plot(displacement(stable_mask) * 1000, force(stable_mask), 'b-', 'LineWidth', 3, 'DisplayName', 'Stable');
plot(displacement(~stable_mask) * 1000, force(~stable_mask), 'r-', 'LineWidth', 3, 'DisplayName', 'Unstable');

grid on;
xlabel('Center Displacement (mm)', 'FontSize', fontsize, 'FontWeight', 'bold');
ylabel('Applied Force (N)', 'FontSize', fontsize, 'FontWeight', 'bold');
title('Critical Force Analysis of Bistable Beam', 'FontSize', fontsize, 'FontWeight', 'bold');
legend('Location', 'best');
set(gca, 'FontSize', fontsize);

% Figure 2: Stiffness Analysis
figure(2);
clf;
plot(displacement * 1000, stiffness_smooth, 'b-', 'LineWidth', 2);
hold on;
yline(0, 'r--', 'LineWidth', 1, 'DisplayName', 'Zero Stiffness');
if ~isempty(zero_crossings)
    plot(displacement(zero_crossings) * 1000, stiffness_smooth(zero_crossings), 'ko', 'MarkerSize', 8, 'MarkerFaceColor', 'black');
end
grid on;
xlabel('Center Displacement (mm)', 'FontSize', fontsize, 'FontWeight', 'bold');
ylabel('Stiffness (N/m)', 'FontSize', fontsize, 'FontWeight', 'bold');
title('Stiffness vs Displacement', 'FontSize', fontsize, 'FontWeight', 'bold');
set(gca, 'FontSize', fontsize);

% Figure 3: S-ratio with Critical Range
figure(3);
clf;
plot(displacement * 1000, S_ratio, 'b-', 'LineWidth', 2);
hold on;
yline(S_crit, 'r--', 'LineWidth', 1, 'DisplayName', sprintf('Critical S-ratio = %.1f', S_crit));
if any(S_range_idx)
    plot(displacement(S_range_idx) * 1000, S_ratio(S_range_idx), 'r-', 'LineWidth', 3, 'DisplayName', 'S-shape Range');
end
grid on;
xlabel('Center Displacement (mm)', 'FontSize', fontsize, 'FontWeight', 'bold');
ylabel('S-ratio', 'FontSize', fontsize, 'FontWeight', 'bold');
title('S-ratio Analysis', 'FontSize', fontsize, 'FontWeight', 'bold');
legend('Location', 'best');
ylim([0, 1]);
set(gca, 'FontSize', fontsize);

% Figure 4: Energy Landscape
figure(4);
clf;
plot(displacement * 1000, U_internal * 1e6, 'b-', 'LineWidth', 2); % Convert to μJ
hold on;
plot(displacement(idx_U_max) * 1000, U_internal(idx_U_max) * 1e6, 'ro', 'MarkerSize', 10, 'MarkerFaceColor', 'red', 'DisplayName', 'Energy Maximum');
grid on;
xlabel('Center Displacement (mm)', 'FontSize', fontsize, 'FontWeight', 'bold');
ylabel('Internal Energy (μJ)', 'FontSize', fontsize, 'FontWeight', 'bold');
title('Energy Landscape', 'FontSize', fontsize, 'FontWeight', 'bold');
legend('Location', 'best');
set(gca, 'FontSize', fontsize);

%% Summary Report

fprintf('\n=== BISTABLE BEAM CRITICAL ANALYSIS SUMMARY ===\n');
fprintf('Geometric parameters:\n');
fprintf('  Length: %.1f mm, Height: %.1f mm, Width: %.1f mm, Thickness: %.1f mm\n', L*1000, h*1000, b*1000, t*1000);
fprintf('  Young''s modulus: %.1f MPa\n', EE/1e6);

fprintf('\nCritical force characteristics:\n');
fprintf('  Forward snap force: %.6f N at %.4f mm displacement\n', F_crit_forward, delta_crit_max*1000);
fprintf('  Reverse snap force: %.6f N at %.4f mm displacement\n', F_crit_reverse, delta_crit_min*1000);
fprintf('  Force hysteresis: %.6f N\n', F_crit_forward - F_crit_reverse);

fprintf('\nStability analysis:\n');
fprintf('  Number of stability transitions: %d\n', length(stability_changes));
fprintf('  Stable displacement percentage: %.1f%%\n', sum(stable_idx)/length(stable_idx)*100);

if any(S_range_idx)
    fprintf('\nS-shape characteristics:\n');
    fprintf('  S-shape displacement range: %.4f mm to %.4f mm\n', S_end_disp*1000, S_start_disp*1000);
    fprintf('  S-shape range length: %.4f mm (%.1f%% of total)\n', S_range_length*1000, abs(S_range_length)/(max(displacement)-min(displacement))*100);
end

fprintf('\nEnergy characteristics:\n');
fprintf('  Forward energy barrier: %.8f J\n', energy_barrier_forward);
fprintf('  Reverse energy barrier: %.8f J\n', energy_barrier_reverse);
fprintf('  Energy asymmetry: %.8f J\n', abs(energy_barrier_forward - energy_barrier_reverse));

fprintf('\nSolution quality:\n');
fprintf('  Failed solutions: %d/%d (%.1f%%)\n', sum(solve_failed), length(solve_failed), sum(solve_failed)/length(solve_failed)*100);

fprintf('\nAnalysis completed with %d displacement points.\n', n_steps);