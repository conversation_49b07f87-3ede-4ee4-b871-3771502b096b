%% SMA-双稳态梁系统：完整动力学分析
% 作者：系统动力学分析
% 描述：非线性动力学、混沌分析和吸引子可视化

clear all; close all; clc;

%% 1. 系统参数设置
global m_eff c a b H L_0 k_M k_A M_s M_f A_s A_f

% 双稳态梁参数
m_eff = 0.01;    % 等效质量 [kg]
c = 0.05;        % 阻尼系数 [N·s/m]
a = 1000.0;      % 势能系数 [N/m³]
b = 10.0;        % 势能系数 [N/m]

% SMA弹簧参数
H = 0.1;         % 弹簧安装高度 [m]
L_0 = 0.08;      % 弹簧初始长度 [m]
k_M = 50.0;      % 马氏体相刚度 [N/m]
k_A = 500.0;     % 奥氏体相刚度 [N/m]

% 相变温度 [°C]
M_s = 50; M_f = 40;    % 马氏体相变开始/结束温度
A_s = 60; A_f = 70;    % 奥氏体相变开始/结束温度

% 计算稳定平衡点
q_stable = sqrt(b/a);  % ≈ ±0.1 m

fprintf('系统参数已初始化\n');
fprintf('稳定平衡点: ±%.4f m\n', q_stable);

%% 2. 创建输出文件夹
output_dir = 'SMA_Beam_Results';
if ~exist(output_dir, 'dir')
    mkdir(output_dir);
end
fprintf('图形将保存到文件夹: %s\n', output_dir);

%% 3. 运行主分析程序
main_analysis();

%% 3. 函数定义部分

function main_analysis()
    % 运行完整的系统分析
    
    fprintf('\n=== 专注于SMA滞回分析 ===\n');
    
    % 只运行SMA滞回分析，跳过耗时的其他分析
    plot_sma_displacement_temperature();
    
    % 如果需要其他分析，可以取消注释以下行：
    % analyze_time_evolution();
    % calculate_lyapunov();
    % plot_bifurcation();
    % plot_poincare_section();
    % analyze_sensitivity();
    % plot_3d_attractor();
end

%% 时间演化分析
function analyze_time_evolution()
    global m_eff c a b H L_0 k_M k_A M_s M_f A_s A_f
    
    % 重新计算稳定平衡点（确保是标量）
    q_stable = sqrt(b/a);
    
    % 初始条件（确保是列向量）
    y0 = zeros(2,1);
    y0(1) = q_stable;  % 初始位置
    y0(2) = 0;         % 初始速度
    
    % 时间范围
    tspan = [0 100];
    
    % 求解ODE
    options = odeset('RelTol', 1e-8, 'AbsTol', 1e-10);
    [t, y] = ode45(@system_ode, tspan, y0, options);
    
    % 计算温度曲线
    T = zeros(length(t),1);
    for i = 1:length(t)
        T(i) = temperature_cycle(t(i));
    end
    
    % 创建图形窗口
    figure('Name', '时间演化和相图', 'Position', [100 100 1200 600]);
    
    % 子图1: 位移随时间变化
    subplot(2,3,1);
    plot(t, y(:,1), 'b-', 'LineWidth', 1);
    xlabel('时间 [s]'); 
    ylabel('位移 q [m]');
    title('位移演化');
    grid on;
    
    % 子图2: 速度随时间变化
    subplot(2,3,2);
    plot(t, y(:,2), 'r-', 'LineWidth', 1);
    xlabel('时间 [s]'); 
    ylabel('速度 dq/dt [m/s]');
    title('速度演化');
    grid on;
    
    % 子图3: 温度随时间变化
    subplot(2,3,3);
    plot(t, T, 'g-', 'LineWidth', 1);
    xlabel('时间 [s]'); 
    ylabel('温度 [°C]');
    title('温度循环');
    grid on;
    
    % 子图4: 相图
    subplot(2,3,4);
    plot(y(:,1), y(:,2), 'b-', 'LineWidth', 0.5);
    xlabel('位置 q [m]'); 
    ylabel('速度 dq/dt [m/s]');
    title('相图');
    grid on;
    axis equal;
    
    % 子图5: 稳态相图（最后20%的数据）
    subplot(2,3,5);
    idx_start = round(0.8*length(t));
    plot(y(idx_start:end,1), y(idx_start:end,2), 'r-', 'LineWidth', 0.5);
    xlabel('位置 q [m]'); 
    ylabel('速度 dq/dt [m/s]');
    title('稳态相图');
    grid on;
    axis equal;
    
    % 子图6: 功率谱
    subplot(2,3,6);
    Fs = 1/mean(diff(t));  % 采样频率
    Y = fft(y(idx_start:end,1));
    L = length(Y);
    P2 = abs(Y/L);
    P1 = P2(1:floor(L/2)+1);
    P1(2:end-1) = 2*P1(2:end-1);
    f = Fs*(0:(L/2))/L;
    semilogy(f, P1, 'b-', 'LineWidth', 1);
    xlabel('频率 [Hz]'); 
    ylabel('|FFT(q)|');
    title('功率谱');
    grid on;
    xlim([0 5]);
    
    % 保存图形
    saveas(gcf, fullfile('SMA_Beam_Results', '01_时间演化和相图.png'));
    saveas(gcf, fullfile('SMA_Beam_Results', '01_时间演化和相图.fig'));
    fprintf('已保存: 时间演化和相图\n');
end

%% Lyapunov指数计算
function calculate_lyapunov()
    global m_eff c a b H L_0 k_M k_A M_s M_f A_s A_f
    
    fprintf('\n正在计算Lyapunov指数...\n');
    
    % 重新计算稳定平衡点
    q_stable = sqrt(b/a);
    
    % 初始条件
    y0 = zeros(2,1);
    y0(1) = q_stable;
    y0(2) = 0;
    
    % 时间参数
    tspan = [0 200];
    dt = 0.1;
    t = tspan(1):dt:tspan(2);
    
    % 初始化
    n = length(y0);
    lyap_sum = zeros(n, 1);
    
    % 初始扰动（单位矩阵）
    Y = zeros(n + n*n, 1);
    Y(1:n) = y0;
    Y(n+1:end) = reshape(eye(n), n*n, 1);
    
    % 求解扩展系统
    options = odeset('RelTol', 1e-9, 'AbsTol', 1e-11);
    [T, Sol] = ode45(@extended_system, t, Y, options);
    
    % 计算Lyapunov指数
    renorm_steps = 0;
    for i = 1:100:length(T)-1
        if i+1 <= length(T)
            % 提取基本矩阵
            Phi = reshape(Sol(i, n+1:end), n, n);
            
            % QR分解进行正交化
            [Q, R] = qr(Phi);
            
            % 累积对角元素的对数
            lyap_sum = lyap_sum + log(abs(diag(R)));
            renorm_steps = renorm_steps + 1;
            
            % 重新归一化（如果不是最后一步）
            if i+1 < length(T)
                Sol(i+1, n+1:end) = reshape(Q, n*n, 1);
            end
        end
    end
    
    % 平均Lyapunov指数
    if renorm_steps > 0
        lyapunov_spectrum = lyap_sum / (renorm_steps * dt * 100);
    else
        lyapunov_spectrum = zeros(n, 1);
    end
    
    % 显示结果
    fprintf('Lyapunov谱: [%.4f, %.4f]\n', lyapunov_spectrum(1), lyapunov_spectrum(2));
    fprintf('最大Lyapunov指数: %.4f\n', max(lyapunov_spectrum));
    
    % 混沌判定
    if max(lyapunov_spectrum) > 0.01
        chaos_status = '混沌';
        color_choice = 'r';
    elseif abs(max(lyapunov_spectrum)) < 0.01
        chaos_status = '边缘混沌';
        color_choice = 'y';
    else
        chaos_status = '规则运动';
        color_choice = 'g';
    end
    
    fprintf('系统行为: %s\n', chaos_status);
    
    % 绘制Lyapunov谱
    figure('Name', 'Lyapunov分析', 'Position', [200 200 600 400]);
    bar(1:n, lyapunov_spectrum, color_choice);
    xlabel('Lyapunov指数索引');
    ylabel('数值');
    title(sprintf('Lyapunov谱 - 系统呈现%s', chaos_status));
    grid on;
    hold on;
    plot([0.5 n+0.5], [0 0], 'k--', 'LineWidth', 1);
    xticks(1:n);
    xticklabels({'\lambda_1', '\lambda_2'});
    
    % 保存图形
    saveas(gcf, fullfile('SMA_Beam_Results', '02_Lyapunov分析.png'));
    saveas(gcf, fullfile('SMA_Beam_Results', '02_Lyapunov分析.fig'));
    fprintf('已保存: Lyapunov分析\n');
end

%% 分岔图绘制
function plot_bifurcation()
    fprintf('\n正在生成分岔图...\n');
    
    % 参数范围（温度振幅）
    T_amplitudes = linspace(5, 40, 50);
    
    % 存储分岔数据
    bif_data = [];
    
    for i = 1:length(T_amplitudes)
        % 初始条件
        y0 = zeros(2,1);
        y0(1) = sqrt(10/1000);
        y0(2) = 0;
        
        % 求解较长时间以达到稳态
        tspan = [0 300];
        options = odeset('RelTol', 1e-8, 'AbsTol', 1e-10);
        
        % 使用匿名函数传递参数
        ode_func = @(t,y) system_ode_bifurcation(t, y, T_amplitudes(i));
        [t, y] = ode45(ode_func, tspan, y0, options);
        
        % 提取最后一部分（稳态）
        if length(y(:,1)) > 5000
            q = y(end-5000:end, 1);
        else
            q = y(:, 1);
        end
        
        % 查找局部极大值
        if length(q) > 3
            [pks, ~] = findpeaks(q);
            
            % 存储最后20个峰值
            if length(pks) > 20
                pks = pks(end-19:end);
            end
            
            for j = 1:length(pks)
                bif_data = [bif_data; T_amplitudes(i), pks(j)];
            end
        end
        
        % 进度指示器
        if mod(i, 10) == 0
            fprintf('进度: %d/%d\n', i, length(T_amplitudes));
        end
    end
    
    % 绘制分岔图
    if ~isempty(bif_data)
        figure('Name', '分岔图', 'Position', [300 300 800 600]);
        plot(bif_data(:,1), bif_data(:,2), 'k.', 'MarkerSize', 1);
        xlabel('温度振幅 [°C]');
        ylabel('位移极大值 q [m]');
        title('温度振幅分岔图');
        grid on;
        
        % 保存图形
        saveas(gcf, fullfile('SMA_Beam_Results', '03_分岔图.png'));
        saveas(gcf, fullfile('SMA_Beam_Results', '03_分岔图.fig'));
        fprintf('已保存: 分岔图\n');
    else
        fprintf('警告：无法生成分岔图数据\n');
    end
end

%% 庞加莱截面
function plot_poincare_section()
    global m_eff c a b H L_0 k_M k_A M_s M_f A_s A_f
    
    fprintf('\n正在生成庞加莱截面...\n');
    
    % 重新计算稳定平衡点
    q_stable = sqrt(b/a);
    
    % 初始条件
    y0 = zeros(2,1);
    y0(1) = q_stable;
    y0(2) = 0;
    
    % 求解ODE
    tspan = [0 500];
    options = odeset('RelTol', 1e-8, 'AbsTol', 1e-10);
    [t, y] = ode45(@system_ode, tspan, y0, options);
    
    % 温度穿越值
    T_cross = 55;  % °C
    
    % 查找庞加莱点（当温度向上穿越T_cross时）
    poincare_points = [];
    
    for i = 2:length(t)
        T_prev = temperature_cycle(t(i-1));
        T_curr = temperature_cycle(t(i));
        
        % 检测向上穿越
        if T_prev < T_cross && T_curr >= T_cross
            % 线性插值
            alpha = (T_cross - T_prev) / (T_curr - T_prev);
            q_cross = y(i-1,1) + alpha * (y(i,1) - y(i-1,1));
            v_cross = y(i-1,2) + alpha * (y(i,2) - y(i-1,2));
            poincare_points = [poincare_points; q_cross, v_cross];
        end
    end
    
    % 绘制庞加莱截面
    figure('Name', '庞加莱截面', 'Position', [400 400 600 600]);
    
    if ~isempty(poincare_points)
        % 跳过瞬态（前20%的点）
        idx_start = max(1, round(0.2 * size(poincare_points, 1)));
        plot(poincare_points(idx_start:end,1), ...
             poincare_points(idx_start:end,2), ...
             'r.', 'MarkerSize', 3);
    end
    
    xlabel('位置 q [m]');
    ylabel('速度 dq/dt [m/s]');
    title(sprintf('庞加莱截面 (T = %.1f°C 穿越)', T_cross));
    grid on;
    axis equal;
    
    % 保存图形
    saveas(gcf, fullfile('SMA_Beam_Results', '04_庞加莱截面.png'));
    saveas(gcf, fullfile('SMA_Beam_Results', '04_庞加莱截面.fig'));
    fprintf('已保存: 庞加莱截面\n');
end

%% 参数敏感性分析
function analyze_sensitivity()
    global m_eff c a b H L_0 k_M k_A M_s M_f A_s A_f
    
    fprintf('\n正在分析参数敏感性...\n');
    
    % 重新计算稳定平衡点
    q_stable = sqrt(b/a);
    
    % 不同的初始扰动
    perturbations = [1e-6, 1e-5, 1e-4, 1e-3];
    
    % 时间范围
    tspan = [0 50];
    options = odeset('RelTol', 1e-8, 'AbsTol', 1e-10);
    
    % 参考解
    y0_ref = zeros(2,1);
    y0_ref(1) = q_stable;
    y0_ref(2) = 0;
    [t_ref, y_ref] = ode45(@system_ode, tspan, y0_ref, options);
    
    % 创建图形
    figure('Name', '敏感性分析', 'Position', [500 100 1200 800]);
    
    for i = 1:length(perturbations)
        % 扰动初始条件
        y0_pert = zeros(2,1);
        y0_pert(1) = q_stable + perturbations(i);
        y0_pert(2) = 0;
        [t_pert, y_pert] = ode45(@system_ode, tspan, y0_pert, options);
        
        % 插值以便比较
        y_pert_interp = interp1(t_pert, y_pert(:,1), t_ref, 'linear', 'extrap');
        
        % 计算偏差
        divergence = abs(y_ref(:,1) - y_pert_interp);
        
        % 绘制结果
        subplot(2,2,i);
        semilogy(t_ref, divergence, 'b-', 'LineWidth', 1.5);
        xlabel('时间 [s]');
        ylabel('|q_{参考} - q_{扰动}| [m]');
        title(sprintf('初始扰动: %.1e m', perturbations(i)));
        grid on;
        ylim([1e-10, 1]);
    end
    
    % 保存图形
    saveas(gcf, fullfile('SMA_Beam_Results', '05_敏感性分析.png'));
    saveas(gcf, fullfile('SMA_Beam_Results', '05_敏感性分析.fig'));
    fprintf('已保存: 敏感性分析\n');
end

%% 三维吸引子可视化
function plot_3d_attractor()
    global m_eff c a b H L_0 k_M k_A M_s M_f A_s A_f
    
    fprintf('\n正在生成三维吸引子...\n');
    
    % 重新计算稳定平衡点
    q_stable = sqrt(b/a);
    
    % 初始条件
    y0 = zeros(2,1);
    y0(1) = q_stable;
    y0(2) = 0;
    
    % 求解较长时间
    tspan = [0 200];
    options = odeset('RelTol', 1e-8, 'AbsTol', 1e-10);
    [t, y] = ode45(@system_ode, tspan, y0, options);
    
    % 跳过瞬态
    idx_start = max(1, round(0.3 * length(t)));
    t = t(idx_start:end);
    y = y(idx_start:end, :);
    
    % 创建延迟坐标进行三维嵌入
    tau = min(20, floor(length(y(:,1))/3));  % 延迟
    
    if 2*tau < length(y(:,1))
        X = y(1:end-2*tau, 1);
        Y = y(tau+1:end-tau, 1);
        Z = y(2*tau+1:end, 1);
        
        % 三维绘图
        figure('Name', '三维吸引子', 'Position', [600 200 800 700]);
        
        % 子图1: 三维轨迹
        subplot(2,2,[1,3]);
        plot3(X, Y, Z, 'b-', 'LineWidth', 0.5);
        xlabel('q(t)'); 
        ylabel('q(t-\tau)'); 
        zlabel('q(t-2\tau)');
        title('三维相空间重构');
        grid on;
        view(45, 30);
        
        % 子图2: XY投影
        subplot(2,2,2);
        plot(X, Y, 'r-', 'LineWidth', 0.5);
        xlabel('q(t)'); 
        ylabel('q(t-\tau)');
        title('XY投影');
        grid on;
        axis equal;
        
        % 子图4: XZ投影
        subplot(2,2,4);
        plot(X, Z, 'g-', 'LineWidth', 0.5);
        xlabel('q(t)'); 
        ylabel('q(t-2\tau)');
        title('XZ投影');
        grid on;
        axis equal;
        
        % 保存图形
        saveas(gcf, fullfile('SMA_Beam_Results', '06_三维吸引子.png'));
        saveas(gcf, fullfile('SMA_Beam_Results', '06_三维吸引子.fig'));
        fprintf('已保存: 三维吸引子\n');
    else
        fprintf('警告：时间序列太短，无法进行三维重构\n');
    end
end

%% ODE系统函数

function dydt = system_ode(t, y)
    global m_eff c a b H L_0
    
    % 确保y是列向量
    if size(y,1) == 1
        y = y';
    end
    
    q = y(1);
    q_dot = y(2);
    
    % 时刻t的温度
    T = temperature_cycle(t);
    
    % SMA刚度
    k = k_SMA(T);
    
    % 系统动力学方程
    q_ddot = (1/m_eff) * (-c*q_dot - a*q^3 + (b + k)*q - k*(H - L_0));
    
    % 返回列向量
    dydt = zeros(2,1);
    dydt(1) = q_dot;
    dydt(2) = q_ddot;
end

function dydt = system_ode_bifurcation(t, y, T_amp)
    global m_eff c a b H L_0
    
    % 确保y是列向量
    if size(y,1) == 1
        y = y';
    end
    
    q = y(1);
    q_dot = y(2);
    
    % 具有可变振幅的温度
    T = 55 + T_amp * sin(2*pi*t/10);
    
    % SMA刚度
    k = k_SMA(T);
    
    % 系统动力学方程
    q_ddot = (1/m_eff) * (-c*q_dot - a*q^3 + (b + k)*q - k*(H - L_0));
    
    % 返回列向量
    dydt = zeros(2,1);
    dydt(1) = q_dot;
    dydt(2) = q_ddot;
end

function dydt = extended_system(t, Y)
    global m_eff c a b H L_0
    
    % 确保Y是列向量
    if size(Y,1) == 1
        Y = Y';
    end
    
    % 提取状态和基本矩阵
    n = 2;
    y = Y(1:n);
    Phi = reshape(Y(n+1:end), n, n);
    
    q = y(1);
    q_dot = y(2);
    
    % 温度
    T = temperature_cycle(t);
    k = k_SMA(T);
    
    % 原始系统
    q_ddot = (1/m_eff) * (-c*q_dot - a*q^3 + (b + k)*q - k*(H - L_0));
    dydt_orig = zeros(n,1);
    dydt_orig(1) = q_dot;
    dydt_orig(2) = q_ddot;
    
    % 雅可比矩阵
    J = [0, 1;
         (1/m_eff)*(-3*a*q^2 + b + k), -c/m_eff];
    
    % 变分方程
    dPhi_dt = J * Phi;
    
    % 组合返回
    dydt = zeros(n + n*n, 1);
    dydt(1:n) = dydt_orig;
    dydt(n+1:end) = reshape(dPhi_dt, n*n, 1);
end

%% 辅助函数

function T = temperature_cycle(t)
    % 温度循环函数
    period = 10.0;  % 周期 [秒]
    T_min = 30;     % 最低温度 [°C]
    T_max = 80;     % 最高温度 [°C]
    T = T_min + (T_max - T_min) * 0.5 * (1 + sin(2*pi*t/period));
end

%% SMA位移vs温度特性图
function plot_sma_displacement_temperature()
    global m_eff c a b H L_0 k_M k_A M_s M_f A_s A_f
    
    fprintf('\n正在生成SMA位移vs温度特性图...\n');
    
    % 温度范围
    T_range = linspace(20, 100, 200);
    
    % 计算SMA刚度随温度变化
    k_values = zeros(size(T_range));
    for i = 1:length(T_range)
        k_values(i) = k_SMA(T_range(i));
    end
    
    % 计算平衡位置随温度变化（简化分析）
    % 在平衡状态下：(b + k)*q - k*(H - L_0) - a*q^3 = 0
    q_equilibrium = zeros(size(T_range));
    
    for i = 1:length(T_range)
        k = k_values(i);
        
        % 求解三次方程：a*q^3 - (b + k)*q + k*(H - L_0) = 0
        % 使用数值方法求解
        coeffs = [a, 0, -(b + k), k*(H - L_0)];
        roots_complex = roots(coeffs);
        
        % 选择实数根
        real_roots = real(roots_complex(abs(imag(roots_complex)) < 1e-10));
        
        if ~isempty(real_roots)
            % 选择最稳定的根（通常是绝对值最小的）
            [~, idx] = min(abs(real_roots));
            q_equilibrium(i) = real_roots(idx);
        else
            q_equilibrium(i) = 0;
        end
    end
    
    % 计算SMA弹簧的应变
    spring_length = H - q_equilibrium;
    strain = (spring_length - L_0) / L_0 * 100;  % 百分比应变
    
    % 创建图形
    figure('Name', 'SMA特性分析', 'Position', [700 100 1200 800]);
    
    % 子图1: SMA刚度vs温度
    subplot(2,3,1);
    plot(T_range, k_values, 'b-', 'LineWidth', 2);
    xlabel('温度 [°C]');
    ylabel('SMA刚度 [N/m]');
    title('SMA刚度随温度变化');
    grid on;
    hold on;
    % 标记相变温度
    plot([M_s M_s], ylim, 'r--', 'LineWidth', 1);
    plot([M_f M_f], ylim, 'r--', 'LineWidth', 1);
    plot([A_s A_s], ylim, 'g--', 'LineWidth', 1);
    plot([A_f A_f], ylim, 'g--', 'LineWidth', 1);
    legend('SMA刚度', 'M_s', 'M_f', 'A_s', 'A_f', 'Location', 'best');
    
    % 子图2: 梁位移vs温度
    subplot(2,3,2);
    plot(T_range, q_equilibrium*1000, 'r-', 'LineWidth', 2);
    xlabel('温度 [°C]');
    ylabel('梁位移 [mm]');
    title('梁平衡位移随温度变化');
    grid on;
    
    % 子图3: SMA应变vs温度
    subplot(2,3,3);
    plot(T_range, strain, 'g-', 'LineWidth', 2);
    xlabel('温度 [°C]');
    ylabel('SMA应变 [%]');
    title('SMA应变随温度变化');
    grid on;
    
    % 子图4: 滞回曲线（加热-冷却循环）- 使用新的滞回模型
    subplot(2,3,4);
    
    % 模拟加热过程 - 使用滞回模型
    T_heating = linspace(20, 100, 100);
    q_heating = zeros(size(T_heating));
    T_prev = T_heating(1) - 1;  % 初始化为升温状态
    q_prev = 0.05;  % 初始位移估计
    
    for i = 1:length(T_heating)
        k = k_SMA_Hysteresis(T_heating(i), T_prev);
        coeffs = [a, 0, -(b + k), k*(H - L_0)];
        q_heating(i) = robust_equilibrium_solver(coeffs, q_prev);
        T_prev = T_heating(i);
        q_prev = q_heating(i);
    end
    
    % 模拟冷却过程 - 使用滞回模型
    T_cooling = linspace(100, 20, 100);
    q_cooling = zeros(size(T_cooling));
    T_prev = T_cooling(1) + 1;  % 初始化为降温状态
    q_prev = q_heating(end);  % 从加热结束位置开始
    
    for i = 1:length(T_cooling)
        k = k_SMA_Hysteresis(T_cooling(i), T_prev);
        coeffs = [a, 0, -(b + k), k*(H - L_0)];
        q_cooling(i) = robust_equilibrium_solver(coeffs, q_prev);
        T_prev = T_cooling(i);
        q_prev = q_cooling(i);
    end
    
    plot(T_heating, q_heating*1000, 'r-', 'LineWidth', 2);
    hold on;
    plot(T_cooling, q_cooling*1000, 'b-', 'LineWidth', 2);
    
    % 添加箭头指示方向
    % 升温箭头
    mid_idx = round(length(T_heating)/2);
    arrow_x = T_heating(mid_idx);
    arrow_y = q_heating(mid_idx)*1000;
    quiver(arrow_x, arrow_y, 5, 0, 'r', 'LineWidth', 2, 'MaxHeadSize', 0.5);
    
    % 降温箭头
    mid_idx = round(length(T_cooling)/2);
    arrow_x = T_cooling(mid_idx);
    arrow_y = q_cooling(mid_idx)*1000;
    quiver(arrow_x, arrow_y, -5, 0, 'b', 'LineWidth', 2, 'MaxHeadSize', 0.5);
    
    xlabel('温度 [°C]');
    ylabel('位移 [mm]');
    title('SMA滞回曲线 (升温vs降温)');
    legend('升温路径', '降温路径', 'Location', 'best');
    grid on;
    
    % 检查滞回效应
    hysteresis_area = trapz(T_heating, q_heating) - trapz(T_cooling, q_cooling);
    fprintf('滞回环面积: %.6f\n', abs(hysteresis_area));
    
    % 子图5: 相变区域放大图
    subplot(2,3,5);
    T_zoom = linspace(35, 75, 200);
    k_zoom = zeros(size(T_zoom));
    q_zoom = zeros(size(T_zoom));
    
    for i = 1:length(T_zoom)
        k_zoom(i) = k_SMA(T_zoom(i));
        coeffs = [a, 0, -(b + k_zoom(i)), k_zoom(i)*(H - L_0)];
        roots_complex = roots(coeffs);
        real_roots = real(roots_complex(abs(imag(roots_complex)) < 1e-10));
        if ~isempty(real_roots)
            [~, idx] = min(abs(real_roots));
            q_zoom(i) = real_roots(idx);
        end
    end
    
    plot(T_zoom, q_zoom*1000, 'k-', 'LineWidth', 2);
    xlabel('温度 [°C]');
    ylabel('位移 [mm]');
    title('相变区域详细图');
    grid on;
    
    % 添加相变温度标记
    hold on;
    plot([M_s M_s], ylim, 'r--', 'LineWidth', 1);
    plot([M_f M_f], ylim, 'r--', 'LineWidth', 1);
    plot([A_s A_s], ylim, 'g--', 'LineWidth', 1);
    plot([A_f A_f], ylim, 'g--', 'LineWidth', 1);
    
    % 子图6: SMA力vs位移特性
    subplot(2,3,6);
    q_test = linspace(-0.05, 0.15, 100);
    
    % 在不同温度下的SMA力
    T_test = [30, 50, 70, 90];
    colors = ['b', 'g', 'r', 'm'];
    
    for j = 1:length(T_test)
        k = k_SMA(T_test(j));
        F_sma = k * (H - q_test - L_0);
        plot(q_test*1000, F_sma, colors(j), 'LineWidth', 1.5);
        hold on;
    end
    
    xlabel('位移 [mm]');
    ylabel('SMA力 [N]');
    title('不同温度下的SMA力特性');
    legend('30°C', '50°C', '70°C', '90°C', 'Location', 'best');
    grid on;
    
    % 保存图形
    saveas(gcf, fullfile('SMA_Beam_Results', '07_SMA特性分析.png'));
    saveas(gcf, fullfile('SMA_Beam_Results', '07_SMA特性分析.fig'));
    fprintf('已保存: SMA特性分析\n');
end

function k = k_SMA(T)
    global k_M k_A A_s A_f
    
    % SMA刚度与温度的关系（平滑过渡）
    T_mid = (A_s + A_f) / 2;
    T_width = (A_f - A_s) / 4;
    
    % 使用tanh函数实现平滑过渡
    if T_width > 0
        k = k_M + (k_A - k_M) * 0.5 * (1 + tanh((T - T_mid) / T_width));
    else
        % 如果温度范围为零，使用阶跃函数
        if T < T_mid
            k = k_M;
        else
            k = k_A;
        end
    end
end

%% 新增：SMA滞回模型函数

function k = k_SMA_Hysteresis(T_current, T_previous)
    % SMA滞回刚度计算函数
    % 输入:
    %   T_current  - 当前温度 [°C]
    %   T_previous - 前一时刻温度 [°C]
    % 输出:
    %   k - SMA刚度 [N/m]
    
    global k_M k_A M_s M_f A_s A_f
    
    % 参数验证
    validate_sma_parameters(M_f, M_s, A_s, A_f);
    
    % 路径判断：升温还是降温
    if T_current >= T_previous
        % 升温路径：使用奥氏体相变
        xi_A = calculate_austenite_fraction(T_current, A_s, A_f);
        k = k_M + (k_A - k_M) * xi_A;
    else
        % 降温路径：使用马氏体相变
        xi_M = calculate_martensite_fraction(T_current, M_s, M_f);
        k = k_A - (k_A - k_M) * xi_M;
    end
    
    % 确保刚度在物理范围内
    k = ensure_stiffness_bounds(k, k_M, k_A);
end

function xi_A = calculate_austenite_fraction(T, A_s, A_f)
    % 计算奥氏体分数（升温过程）
    % 输入:
    %   T   - 当前温度 [°C]
    %   A_s - 奥氏体相变开始温度 [°C]
    %   A_f - 奥氏体相变结束温度 [°C]
    % 输出:
    %   xi_A - 奥氏体分数 [0, 1]
    
    if A_f <= A_s
        % 处理退化情况
        if T >= A_s
            xi_A = 1.0;
        else
            xi_A = 0.0;
        end
        return;
    end
    
    T_mid = (A_s + A_f) / 2;
    T_width = (A_f - A_s) / 4;
    
    % 使用tanh函数实现平滑过渡
    xi_A = 0.5 * (1 + tanh((T - T_mid) / T_width));
    
    % 确保分数在[0, 1]范围内
    xi_A = max(0, min(1, xi_A));
end

function xi_M = calculate_martensite_fraction(T, M_s, M_f)
    % 计算马氏体分数（降温过程）
    % 输入:
    %   T   - 当前温度 [°C]
    %   M_s - 马氏体相变开始温度 [°C]
    %   M_f - 马氏体相变结束温度 [°C]
    % 输出:
    %   xi_M - 马氏体分数 [0, 1]
    
    if M_s <= M_f
        % 处理退化情况
        if T <= M_s
            xi_M = 1.0;
        else
            xi_M = 0.0;
        end
        return;
    end
    
    T_mid = (M_s + M_f) / 2;
    T_width = (M_s - M_f) / 4;  % 注意：降温过程，M_s > M_f
    
    % 使用tanh函数实现平滑过渡（降温过程）
    xi_M = 0.5 * (1 + tanh((T_mid - T) / T_width));
    
    % 确保分数在[0, 1]范围内
    xi_M = max(0, min(1, xi_M));
end

function validate_sma_parameters(M_f, M_s, A_s, A_f)
    % 验证SMA相变温度参数的物理合理性
    % 输入: M_f, M_s, A_s, A_f - 相变温度参数 [°C]
    
    % 检查基本物理约束：M_f < M_s < A_s < A_f
    if ~(M_f < M_s && M_s < A_s && A_s < A_f)
        error('SMA参数错误: 必须满足 M_f < M_s < A_s < A_f\n当前值: M_f=%.1f, M_s=%.1f, A_s=%.1f, A_f=%.1f', ...
              M_f, M_s, A_s, A_f);
    end
    
    % 检查相变温度范围
    if (M_s - M_f) < 1
        warning('马氏体相变温度范围过小 (%.1f°C)，可能导致数值不稳定', M_s - M_f);
    end
    
    if (A_f - A_s) < 1
        warning('奥氏体相变温度范围过小 (%.1f°C)，可能导致数值不稳定', A_f - A_s);
    end
    
    % 检查滞回宽度
    hysteresis_width = A_s - M_s;
    if hysteresis_width < 5
        warning('滞回宽度过小 (%.1f°C)，滞回效应可能不明显', hysteresis_width);
    end
end

function k = ensure_stiffness_bounds(k, k_M, k_A)
    % 确保刚度在物理范围内
    % 输入:
    %   k   - 计算得到的刚度
    %   k_M - 马氏体刚度
    %   k_A - 奥氏体刚度
    % 输出:
    %   k - 修正后的刚度
    
    k_min = min(k_M, k_A);
    k_max = max(k_M, k_A);
    
    if k < k_min
        k = k_min;
        warning('刚度值过小，已修正为最小值: %.2f N/m', k_min);
    elseif k > k_max
        k = k_max;
        warning('刚度值过大，已修正为最大值: %.2f N/m', k_max);
    end
end

function q = robust_equilibrium_solver(coeffs, q_prev)
    % 鲁棒的平衡位移求解器
    % 输入:
    %   coeffs - 三次方程系数 [a, 0, -(b+k), k*(H-L_0)]
    %   q_prev - 前一步的位移值 [m]
    % 输出:
    %   q - 当前步的平衡位移 [m]
    
    try
        % 尝试求解三次方程
        roots_complex = roots(coeffs);
        
        % 选择物理上合理的根
        q = select_physical_root(roots_complex, q_prev);
        
        % 检查结果的合理性
        if isnan(q) || isinf(q)
            warning('根求解结果异常，使用前一步结果');
            q = q_prev;
        end
        
    catch ME
        warning('根求解失败，使用前一步结果: %s', ME.message);
        q = q_prev;
    end
end

function q_selected = select_physical_root(roots_array, q_previous)
    % 选择物理上合理的实数根
    % 输入:
    %   roots_array - 复数根数组
    %   q_previous  - 前一步位移值 [m]
    % 输出:
    %   q_selected - 选择的实数根 [m]
    
    % 提取实数根（虚部小于容差的根）
    tolerance = 1e-10;
    real_roots = real(roots_array(abs(imag(roots_array)) < tolerance));
    
    if isempty(real_roots)
        % 如果没有实数根，返回前一步结果
        warning('未找到实数根，保持前一步位移');
        q_selected = q_previous;
        return;
    end
    
    % 选择最接近前一步位移的根
    [~, idx] = min(abs(real_roots - q_previous));
    q_selected = real_roots(idx);
    
    % 额外的物理合理性检查
    if abs(q_selected) > 1.0  % 位移过大的检查
        warning('选择的根位移过大 (%.3f m)，可能不合理', q_selected);
    end
end



%% 单元测试函数

function test_sma_hysteresis_functions()
    % 测试SMA滞回函数的正确性
    fprintf('\n=== 开始SMA滞回函数单元测试 ===\n');
    
    global k_M k_A M_s M_f A_s A_f
    
    % 测试参数
    test_passed = true;
    
    % 测试1: 奥氏体分数计算
    fprintf('测试1: 奥氏体分数计算...\n');
    xi_A_low = calculate_austenite_fraction(A_s - 10, A_s, A_f);  % 应该接近0
    xi_A_mid = calculate_austenite_fraction((A_s + A_f)/2, A_s, A_f);  % 应该接近0.5
    xi_A_high = calculate_austenite_fraction(A_f + 10, A_s, A_f);  % 应该接近1
    
    if xi_A_low < 0.1 && abs(xi_A_mid - 0.5) < 0.1 && xi_A_high > 0.9
        fprintf('  ✓ 奥氏体分数计算正确: 低温=%.3f, 中温=%.3f, 高温=%.3f\n', ...
                xi_A_low, xi_A_mid, xi_A_high);
    else
        fprintf('  ✗ 奥氏体分数计算错误\n');
        test_passed = false;
    end
    
    % 测试2: 马氏体分数计算
    fprintf('测试2: 马氏体分数计算...\n');
    xi_M_high = calculate_martensite_fraction(M_s + 10, M_s, M_f);  % 应该接近0
    xi_M_mid = calculate_martensite_fraction((M_s + M_f)/2, M_s, M_f);  % 应该接近0.5
    xi_M_low = calculate_martensite_fraction(M_f - 10, M_s, M_f);  % 应该接近1
    
    if xi_M_high < 0.1 && abs(xi_M_mid - 0.5) < 0.1 && xi_M_low > 0.9
        fprintf('  ✓ 马氏体分数计算正确: 高温=%.3f, 中温=%.3f, 低温=%.3f\n', ...
                xi_M_high, xi_M_mid, xi_M_low);
    else
        fprintf('  ✗ 马氏体分数计算错误\n');
        test_passed = false;
    end
    
    % 测试3: 滞回函数路径判断
    fprintf('测试3: 滞回函数路径判断...\n');
    T1 = 30; T2 = 80;  % 升温
    k_heating = k_SMA_Hysteresis(T2, T1);
    k_cooling = k_SMA_Hysteresis(T1, T2);  % 降温
    
    if k_heating ~= k_cooling
        fprintf('  ✓ 滞回路径判断正确: 升温刚度=%.2f, 降温刚度=%.2f\n', ...
                k_heating, k_cooling);
    else
        fprintf('  ✗ 滞回路径判断错误，升温和降温刚度相同\n');
        test_passed = false;
    end
    
    % 测试4: 刚度边界检查
    fprintf('测试4: 刚度边界检查...\n');
    k_test = ensure_stiffness_bounds(k_A + 100, k_M, k_A);  % 超出上界
    if k_test <= max(k_M, k_A)
        fprintf('  ✓ 刚度边界检查正确\n');
    else
        fprintf('  ✗ 刚度边界检查失败\n');
        test_passed = false;
    end
    
    % 测试5: 根选择函数
    fprintf('测试5: 根选择函数...\n');
    test_roots = [0.05 + 0.001i, 0.1, -0.08, 0.15];  % 包含复数根和多个实数根
    q_prev = 0.09;
    q_selected = select_physical_root(test_roots, q_prev);
    if abs(q_selected - 0.1) < 0.01  % 应该选择最接近0.09的根0.1
        fprintf('  ✓ 根选择函数正确: 选择根=%.3f\n', q_selected);
    else
        fprintf('  ✗ 根选择函数错误: 选择根=%.3f\n', q_selected);
        test_passed = false;
    end
    
    % 测试6: 鲁棒求解器
    fprintf('测试6: 鲁棒求解器...\n');
    global a b k_M H L_0
    k_test = (k_M + k_A) / 2;
    coeffs = [a, 0, -(b + k_test), k_test*(H - L_0)];
    q_prev = 0.08;
    q_solved = robust_equilibrium_solver(coeffs, q_prev);
    if ~isnan(q_solved) && ~isinf(q_solved)
        fprintf('  ✓ 鲁棒求解器正确: 求解结果=%.4f\n', q_solved);
    else
        fprintf('  ✗ 鲁棒求解器失败\n');
        test_passed = false;
    end
    
    % 测试结果
    if test_passed
        fprintf('=== 所有测试通过 ✓ ===\n');
    else
        fprintf('=== 部分测试失败 ✗ ===\n');
    end
end

function test_hysteresis_loop()
    % 专门测试滞回环的形成
    fprintf('\n=== 滞回环测试 ===\n');
    
    global a b H L_0 k_M k_A M_s M_f A_s A_f
    
    % 创建简单的滞回测试
    figure('Name', '滞回环测试', 'Position', [100 100 800 600]);
    
    % 温度循环：20°C -> 100°C -> 20°C
    T_up = linspace(20, 100, 50);
    T_down = linspace(100, 20, 50);
    
    % 升温过程
    q_up = zeros(size(T_up));
    T_prev = 15;  % 确保第一步是升温
    q_prev = 0.05;
    
    for i = 1:length(T_up)
        k = k_SMA_Hysteresis(T_up(i), T_prev);
        coeffs = [a, 0, -(b + k), k*(H - L_0)];
        q_up(i) = robust_equilibrium_solver(coeffs, q_prev);
        T_prev = T_up(i);
        q_prev = q_up(i);
    end
    
    % 降温过程
    q_down = zeros(size(T_down));
    T_prev = 105;  % 确保第一步是降温
    q_prev = q_up(end);
    
    for i = 1:length(T_down)
        k = k_SMA_Hysteresis(T_down(i), T_prev);
        coeffs = [a, 0, -(b + k), k*(H - L_0)];
        q_down(i) = robust_equilibrium_solver(coeffs, q_prev);
        T_prev = T_down(i);
        q_prev = q_down(i);
    end
    
    % 绘制滞回环
    plot(T_up, q_up*1000, 'r-', 'LineWidth', 3, 'DisplayName', '升温路径');
    hold on;
    plot(T_down, q_down*1000, 'b-', 'LineWidth', 3, 'DisplayName', '降温路径');
    
    % 添加起点和终点标记
    plot(T_up(1), q_up(1)*1000, 'go', 'MarkerSize', 8, 'MarkerFaceColor', 'g', 'DisplayName', '起点');
    plot(T_down(end), q_down(end)*1000, 'ro', 'MarkerSize', 8, 'MarkerFaceColor', 'r', 'DisplayName', '终点');
    
    % 添加相变温度线
    ylims = ylim;
    plot([M_f M_f], ylims, 'k--', 'LineWidth', 1, 'DisplayName', 'M_f');
    plot([M_s M_s], ylims, 'k--', 'LineWidth', 1, 'DisplayName', 'M_s');
    plot([A_s A_s], ylims, 'k--', 'LineWidth', 1, 'DisplayName', 'A_s');
    plot([A_f A_f], ylims, 'k--', 'LineWidth', 1, 'DisplayName', 'A_f');
    
    xlabel('温度 [°C]');
    ylabel('位移 [mm]');
    title('SMA滞回环测试');
    legend('Location', 'best');
    grid on;
    
    % 计算滞回特征
    max_separation = max(abs(interp1(T_down, q_down, T_up(2:end-1)) - q_up(2:end-1)));
    fprintf('最大路径分离: %.4f mm\n', max_separation*1000);
    
    % 检查是否形成闭合环
    closure_error = norm([T_up(1) - T_down(end), q_up(1) - q_down(end)]);
    fprintf('闭合误差: %.6f\n', closure_error);
    
    if max_separation > 1e-6
        fprintf('✓ 成功形成滞回环！\n');
    else
        fprintf('✗ 未形成明显的滞回环\n');
    end
end

%% 脚本结束
fprintf('\n=== 分析完成 ===\n');
fprintf('所有图形已生成并保存到 SMA_Beam_Results 文件夹中。\n');
fprintf('保存的文件包括：\n');
fprintf('1. 01_时间演化和相图.png/.fig\n');
fprintf('2. 02_Lyapunov分析.png/.fig\n');
fprintf('3. 03_分岔图.png/.fig\n');
fprintf('4. 04_庞加莱截面.png/.fig\n');
fprintf('5. 05_敏感性分析.png/.fig\n');
fprintf('6. 06_三维吸引子.png/.fig\n');
fprintf('7. 07_SMA特性分析.png/.fig\n');
fprintf('\n图形文件位置: %s\n', fullfile(pwd, 'SMA_Beam_Results'));

% 运行单元测试
test_sma_hysteresis_functions();

% 运行专门的滞回测试
test_hysteresis_loop();