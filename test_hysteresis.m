%% 简单的SMA滞回测试脚本
clear; close all; clc;

% 设置全局参数
global m_eff c a b H L_0 k_M k_A M_s M_f A_s A_f

% 双稳态梁参数
m_eff = 0.01;    % 等效质量 [kg]
c = 0.05;        % 阻尼系数 [N·s/m]
a = 1000.0;      % 势能系数 [N/m³]
b = 10.0;        % 势能系数 [N/m]

% SMA弹簧参数
H = 0.1;         % 弹簧安装高度 [m]
L_0 = 0.08;      % 弹簧初始长度 [m]
k_M = 50.0;      % 马氏体相刚度 [N/m]
k_A = 500.0;     % 奥氏体相刚度 [N/m]

% 相变温度 [°C]
M_s = 50; M_f = 40;    % 马氏体相变开始/结束温度
A_s = 60; A_f = 70;    % 奥氏体相变开始/结束温度

fprintf('参数设置完成\n');
fprintf('相变温度: M_f=%.1f, M_s=%.1f, A_s=%.1f, A_f=%.1f\n', M_f, M_s, A_s, A_f);

% 测试滞回函数
fprintf('\n测试滞回函数...\n');

% 测试升温和降温的刚度差异
T_test = 65;  % 在相变区域内的温度
k_heating = k_SMA_Hysteresis(T_test, T_test-10);  % 升温
k_cooling = k_SMA_Hysteresis(T_test, T_test+10);  % 降温

fprintf('温度%.1f°C时:\n', T_test);
fprintf('  升温刚度: %.2f N/m\n', k_heating);
fprintf('  降温刚度: %.2f N/m\n', k_cooling);
fprintf('  刚度差异: %.2f N/m\n', abs(k_heating - k_cooling));

if abs(k_heating - k_cooling) > 1
    fprintf('✓ 滞回效应正常！\n');
else
    fprintf('✗ 滞回效应不明显\n');
end

% 绘制简单的滞回环
figure('Name', 'SMA滞回测试', 'Position', [100 100 800 600]);

% 温度范围
T_up = linspace(30, 90, 30);
T_down = linspace(90, 30, 30);

% 升温过程
q_up = zeros(size(T_up));
T_prev = 25;
q_prev = 0.05;

for i = 1:length(T_up)
    k = k_SMA_Hysteresis(T_up(i), T_prev);
    coeffs = [a, 0, -(b + k), k*(H - L_0)];
    roots_complex = roots(coeffs);
    real_roots = real(roots_complex(abs(imag(roots_complex)) < 1e-10));
    if ~isempty(real_roots)
        [~, idx] = min(abs(real_roots - q_prev));
        q_up(i) = real_roots(idx);
    else
        q_up(i) = q_prev;
    end
    T_prev = T_up(i);
    q_prev = q_up(i);
end

% 降温过程
q_down = zeros(size(T_down));
T_prev = 95;
q_prev = q_up(end);

for i = 1:length(T_down)
    k = k_SMA_Hysteresis(T_down(i), T_prev);
    coeffs = [a, 0, -(b + k), k*(H - L_0)];
    roots_complex = roots(coeffs);
    real_roots = real(roots_complex(abs(imag(roots_complex)) < 1e-10));
    if ~isempty(real_roots)
        [~, idx] = min(abs(real_roots - q_prev));
        q_down(i) = real_roots(idx);
    else
        q_down(i) = q_prev;
    end
    T_prev = T_down(i);
    q_prev = q_down(i);
end

% 绘制结果
plot(T_up, q_up*1000, 'r-', 'LineWidth', 3, 'DisplayName', '升温路径');
hold on;
plot(T_down, q_down*1000, 'b-', 'LineWidth', 3, 'DisplayName', '降温路径');

% 标记相变温度
ylims = ylim;
plot([M_f M_f], ylims, 'k--', 'LineWidth', 1, 'DisplayName', 'M_f');
plot([M_s M_s], ylims, 'k--', 'LineWidth', 1, 'DisplayName', 'M_s');
plot([A_s A_s], ylims, 'k--', 'LineWidth', 1, 'DisplayName', 'A_s');
plot([A_f A_f], ylims, 'k--', 'LineWidth', 1, 'DisplayName', 'A_f');

xlabel('温度 [°C]');
ylabel('位移 [mm]');
title('SMA滞回环');
legend('Location', 'best');
grid on;

% 计算滞回特征
max_diff = max(abs(q_up - interp1(T_down, q_down, T_up, 'linear', 'extrap')));
fprintf('\n滞回分析结果:\n');
fprintf('最大路径分离: %.4f mm\n', max_diff*1000);

if max_diff > 1e-5
    fprintf('✓ 成功形成滞回环！\n');
else
    fprintf('✗ 滞回环不明显\n');
end

%% 滞回函数定义

function k = k_SMA_Hysteresis(T_current, T_previous)
    global k_M k_A M_s M_f A_s A_f
    
    % 路径判断：升温还是降温
    if T_current >= T_previous
        % 升温路径：使用奥氏体相变
        xi_A = calculate_austenite_fraction(T_current, A_s, A_f);
        k = k_M + (k_A - k_M) * xi_A;
    else
        % 降温路径：使用马氏体相变
        xi_M = calculate_martensite_fraction(T_current, M_s, M_f);
        k = k_A - (k_A - k_M) * xi_M;
    end
end

function xi_A = calculate_austenite_fraction(T, A_s, A_f)
    if A_f <= A_s
        if T >= A_s
            xi_A = 1.0;
        else
            xi_A = 0.0;
        end
        return;
    end
    
    T_mid = (A_s + A_f) / 2;
    T_width = (A_f - A_s) / 4;
    xi_A = 0.5 * (1 + tanh((T - T_mid) / T_width));
    xi_A = max(0, min(1, xi_A));
end

function xi_M = calculate_martensite_fraction(T, M_s, M_f)
    if M_s <= M_f
        if T <= M_s
            xi_M = 1.0;
        else
            xi_M = 0.0;
        end
        return;
    end
    
    T_mid = (M_s + M_f) / 2;
    T_width = (M_s - M_f) / 4;
    xi_M = 0.5 * (1 + tanh((T_mid - T) / T_width));
    xi_M = max(0, min(1, xi_M));
end